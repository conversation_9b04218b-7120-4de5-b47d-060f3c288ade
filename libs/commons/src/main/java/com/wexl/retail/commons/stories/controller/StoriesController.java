package com.wexl.retail.commons.stories.controller;

import com.wexl.retail.commons.stories.dto.StoriesDto;
import com.wexl.retail.commons.stories.service.StoriesService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class StoriesController {

  private final StoriesService storyWaverService;

  @GetMapping("/stories")
  public List<StoriesDto.Response> getStoryWaverData(@RequestParam(name = "type") String type) {
    return storyWaverService.getStoryWaverData(type);
  }
}
