package com.wexl.retail.commons.stories.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.stories.dto.StoriesDto;
import com.wexl.retail.commons.stories.dto.StoriesProperties;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class StoriesService {
  private final StoriesProperties storyWaverProperties;

  public List<StoriesDto.Response> getStoryWaverData(String type) {
    var result = storyWaverProperties.getTypes().get(type);
    if (result == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "No such type: " + type);
    }
    return result;
  }
}
