package com.wexl.retail.erp.attendance.service;

import static com.wexl.retail.commons.util.DateTimeUtil.getEpochFromStringDate;
import static java.util.Objects.isNull;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.calenderevent.dto.CalenderEventDto;
import com.wexl.retail.classroom.core.dto.ScheduleInstAttendanceStatus;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.erp.attendance.domain.CompletionStatus;
import com.wexl.retail.erp.attendance.domain.SectionAttendance;
import com.wexl.retail.erp.attendance.domain.SectionAttendanceDetails;
import com.wexl.retail.erp.attendance.dto.*;
import com.wexl.retail.erp.attendance.dto.Month;
import com.wexl.retail.erp.attendance.dto.StudentAttendanceReportDto;
import com.wexl.retail.erp.attendance.dto.StudentsAttendanceReport;
import com.wexl.retail.erp.attendance.publisher.StudentAttendanceEventPublisher;
import com.wexl.retail.erp.attendance.repository.CalenderRepository;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceDetailRepository;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceRepository;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.mlp.service.MlpService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.domain.TeacherSubjects;
import com.wexl.retail.section.dto.response.SectionEntityDto;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.repository.TeacherSectionRepository;
import com.wexl.retail.section.repository.TeacherSubjectsRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.StrapiContentHelper;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import jakarta.transaction.Transactional;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ErpAttendanceService {
  private final CalenderRepository calenderRepository;
  private final OrganizationRepository organizationRepository;
  private final SectionService sectionService;
  private final SectionAttendanceRepository sectionAttendanceRepository;
  private final DateTimeUtil dateTimeUtil;
  private final StudentRepository studentRepository;
  private final SectionAttendanceDetailRepository sectionAttendanceDetailRepository;
  private final UserRepository userRepository;
  private final SectionRepository sectionRepository;
  private final StrapiContentHelper strapiContentHelper;
  private final TeacherSubjectsRepository teacherSubjectsRepository;
  private final ValidationUtils validationUtils;
  private final ReportCardService reportCardService;
  private final UserService userService;
  private final TeacherRepository teacherRepository;
  private final NotificationsService notificationService;
  private final MlpService mlpService;
  private final AuthService authService;
  private final StudentAttendanceEventPublisher studentAttendanceEventPublisher;
  private final TeacherSectionRepository teacherSectionRepository;
  private final StrapiService strapiService;
  private final UserRoleHelper userRoleHelper;

  private static final String ADMISSION_NO = "admission_no";
  private static final String ATTENDEES = "attendees_count";
  private static final String ABSENTEES = "absentees_count";
  private static final String LEAVES = "leaves_count";
  private static final String LATE_COMER = "late_comer";
  private static final String HALF_DAY = "half_day";
  private static final String TOTAL_STRENGTH = "total_count";
  private static final String AFTERNOON = "afternoon";
  private static final String MORNING = "morning";
  private static final String PTM = "ptm_count";
  private static final String NID = "nid_count";

  @Transactional
  public void initiateCalendarData(String orgId, FromDate.Request request) {
    Date currentDate;

    if (request.fromDate() != null) {
      currentDate = dateTimeUtil.convertEpochToTimestamp(request.fromDate());
    } else {
      currentDate = dateTimeUtil.getCurrentDate();
    }
    var convertedDate = dateTimeUtil.convertToIntegerFormat(currentDate);
    var calenderData = calenderRepository.findAllByDate(convertedDate);
    var organization = organizationRepository.findBySlug(orgId);
    List<SectionAttendanceRequest> attendanceRequestList = new ArrayList<>();
    var sections = sectionService.getAllSections(orgId, false);
    var sectionList = sections.stream().map(SectionEntityDto.Response::id).toList();
    var getSectionsData = sectionAttendanceRepository.getSectionsData(sectionList);

    sections.forEach(
        section ->
            calenderData.forEach(
                calenderDetails -> {
                  var sectionAttendance =
                      getSectionsData.stream()
                          .filter(
                              s ->
                                  s.getDateId().equals(calenderDetails.getDateId())
                                      && s.getSection().getId() == section.id())
                          .findFirst();
                  var sectionEntity = sectionService.findSectionById(section.id());
                  if (sectionAttendance.isEmpty()) {
                    SectionAttendanceRequest attendanceRequest = new SectionAttendanceRequest();
                    attendanceRequest.setSection(sectionEntity);
                    attendanceRequest.setCalender(calenderDetails);
                    attendanceRequest.setOrg(organization);
                    attendanceRequest.setDateId(calenderDetails.getDateId());
                    attendanceRequest.setAttendanceStatus(CompletionStatus.NOTCOMPLETED);
                    attendanceRequestList.add(attendanceRequest);
                  }
                }));
    var sectionAttendanceList = buildSectionAttendanceRepository(attendanceRequestList);
    sectionAttendanceRepository.saveAll(sectionAttendanceList);
  }

  private List<SectionAttendance> buildSectionAttendanceRepository(
      List<SectionAttendanceRequest> attendanceRequestList) {
    List<SectionAttendance> sectionAttendanceList = new ArrayList<>();
    attendanceRequestList.forEach(
        sectionAttendanceRequest -> {
          SectionAttendance sectionAttendance = new SectionAttendance();
          sectionAttendance.setSection(sectionAttendanceRequest.getSection());
          sectionAttendance.setOrg(sectionAttendanceRequest.getOrg());
          sectionAttendance.setCalenderDetails(sectionAttendanceRequest.getCalender());
          sectionAttendance.setTeacher(sectionAttendanceRequest.getTeacher());
          sectionAttendance.setDateId(sectionAttendanceRequest.getDateId());
          sectionAttendance.setStatus(sectionAttendance.getStatus());
          sectionAttendanceList.add(sectionAttendance);
        });
    return sectionAttendanceList;
  }

  public void updateAfternoonAttendance(AddAttendanceRequest request) {
    List<SectionAttendanceDetails> sectionAttendanceDetailsList = new ArrayList<>();
    request
        .getStudents()
        .forEach(
            studentDetails -> {
              var sectionAttendanceDetailsRequest =
                  sectionAttendanceDetailRepository
                      .findById(studentDetails.getSectionAttendanceDetailsId())
                      .orElseThrow(
                          () ->
                              new ApiException(
                                  InternalErrorCodes.INVALID_REQUEST,
                                  "error.InvalidSectionAttendanceDetailsId"));
              sectionAttendanceDetailsRequest.setAfternoonAttendanceStatus(
                  studentDetails.getAttendanceStatus());
              sectionAttendanceDetailsRequest.setEntryTime(
                  studentDetails.getEntryTime() != null
                      ? dateTimeUtil.convertEpochToIso8601Legacy(studentDetails.getEntryTime())
                      : null);
              sectionAttendanceDetailsRequest.setExitTime(
                  studentDetails.getExitTime() != null
                      ? dateTimeUtil.convertEpochToIso8601Legacy(studentDetails.getExitTime())
                      : null);
              sectionAttendanceDetailsRequest.setNote(studentDetails.getNote());
              sectionAttendanceDetailsList.add(sectionAttendanceDetailsRequest);
            });
    sectionAttendanceDetailRepository.saveAll(sectionAttendanceDetailsList);
    if (!sectionAttendanceDetailsList.isEmpty()) {
      notificationService.triggerAttendanceNotification(
          sectionAttendanceDetailsList.get(0).getSectionAttendance(), AFTERNOON, request.getDate());
    }
  }

  @Transactional
  public void addAttendance(Integer sectionId, Long attendanceId, AddAttendanceRequest request) {
    var possibleSectionAttendance = sectionAttendanceRepository.findById(attendanceId);
    var studentsList = request.getStudents().stream().map(StudentDetails::getStudent).toList();
    var teacher = authService.getTeacherDetails();
    Teacher teacherInfo = null;
    if (request.getTeacher() != null) {
      var teacherDetails = teacherRepository.findById(request.getTeacher());
      teacherInfo = teacherDetails.stream().findFirst().orElse(null);
    }
    var studentsDetails = studentRepository.findAllById(studentsList);
    if (possibleSectionAttendance.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidAttendanceId");
    }
    final SectionAttendance sectionAttendance = possibleSectionAttendance.get();
    validateRequest(sectionId, sectionAttendance, studentsDetails);
    List<SectionAttendanceDetailsRequest> sectionAttendanceDetailsRequestList = new ArrayList<>();
    boolean isAllHoliday =
        request.getStudents().stream()
            .map(StudentDetails::getAttendanceStatus)
            .allMatch("holiday"::equalsIgnoreCase);
    request
        .getStudents()
        .forEach(
            studentDetails -> {
              var student =
                  studentsDetails.stream()
                      .filter(s -> s.getId() == studentDetails.getStudent())
                      .findFirst();
              SectionAttendanceDetailsRequest sectionAttendanceDetailsRequest =
                  new SectionAttendanceDetailsRequest();
              sectionAttendanceDetailsRequest.setStudent(student.get());
              sectionAttendanceDetailsRequest.setAttendanceStatus(
                  studentDetails.getAttendanceStatus());
              sectionAttendanceDetailsRequest.setSectionAttendance(sectionAttendance);
              sectionAttendanceDetailsRequest.setEntryTime(studentDetails.getEntryTime());
              sectionAttendanceDetailsRequest.setExitTime(studentDetails.getExitTime());
              sectionAttendanceDetailsRequest.setNote(studentDetails.getNote());
              sectionAttendanceDetailsRequestList.add(sectionAttendanceDetailsRequest);
            });
    var attendanceRequest =
        buildAttendanceDetails(sectionAttendanceDetailsRequestList, sectionAttendance.getId());
    sectionAttendance.setStatus(
        isAllHoliday ? CompletionStatus.HOLIDAY : CompletionStatus.COMPLETED);
    sectionAttendance.setAttendanceDetails(attendanceRequest);
    sectionAttendance.setTeacher(teacherInfo != null ? teacherInfo : teacher.getTeacherInfo());
    sectionAttendanceRepository.save(sectionAttendance);
    studentAttendanceEventPublisher.publishStudentAttendance(request);
    notificationService.triggerAttendanceNotification(
        sectionAttendance, MORNING, request.getDate());
  }

  private void validateRequest(
      Integer sectionId, SectionAttendance sectionAttendance, List<Student> studentsList) {
    if (sectionAttendance.getSection().getId() != sectionId) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.UnAuthorized");
    }
    if (studentsList.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentsNone.Attendance");
    }
    studentsList.forEach(
        student -> {
          var studentDetail = student.getSection();
          if (studentDetail.getId() != sectionAttendance.getSection().getId()
              && studentDetail
                  .getOrganization()
                  .equals(sectionAttendance.getSection().getOrganization())) {
            throw new ApiException(
                InternalErrorCodes.UN_AUTHORIZED,
                "error.Student.Unauthorized",
                new String[] {Long.toString(student.getId())});
          }
        });
  }

  private List<SectionAttendanceDetails> buildAttendanceDetails(
      List<SectionAttendanceDetailsRequest> sectionAttendanceDetailsRequestList, Long sectionId) {
    List<SectionAttendanceDetails> sectionAttendanceDetailsList = new ArrayList<>();
    List<SectionAttendanceDetails> sectionAttendanceDetails =
        sectionAttendanceDetailRepository.findBySectionAttendanceId(sectionId);

    sectionAttendanceDetailsRequestList.forEach(
        sectionAttendanceDetailsRequest -> {
          SectionAttendanceDetails attendanceDetails = new SectionAttendanceDetails();
          var sectionAttendanceDetail =
              sectionAttendanceDetails.stream()
                  .filter(
                      s ->
                          s.getStudent().getId()
                              == sectionAttendanceDetailsRequest.getStudent().getId())
                  .findFirst();
          if (sectionAttendanceDetail.isPresent()) {
            sectionAttendanceDetail.stream()
                .forEach(
                    sectionAttendanceDetails1 -> {
                      sectionAttendanceDetails1.setAttendanceStatus(
                          sectionAttendanceDetailsRequest.getAttendanceStatus());
                      sectionAttendanceDetails1.setAfternoonAttendanceStatus(
                          sectionAttendanceDetailsRequest.getAttendanceStatus());
                      sectionAttendanceDetails1.setEntryTime(
                          sectionAttendanceDetailsRequest.getEntryTime() != null
                              ? dateTimeUtil.convertEpochToIso8601Legacy(
                                  sectionAttendanceDetailsRequest.getEntryTime())
                              : null);
                      sectionAttendanceDetails1.setExitTime(
                          sectionAttendanceDetailsRequest.getExitTime() != null
                              ? dateTimeUtil.convertEpochToIso8601Legacy(
                                  sectionAttendanceDetailsRequest.getExitTime())
                              : null);
                      sectionAttendanceDetails1.setNote(sectionAttendanceDetailsRequest.getNote());
                      sectionAttendanceDetailsList.add(sectionAttendanceDetails1);
                    });
          } else {
            attendanceDetails.setId(null);
            attendanceDetails.setStudent(sectionAttendanceDetailsRequest.getStudent());
            attendanceDetails.setAttendanceStatus(
                sectionAttendanceDetailsRequest.getAttendanceStatus());
            attendanceDetails.setAfternoonAttendanceStatus(
                sectionAttendanceDetailsRequest.getAttendanceStatus());
            attendanceDetails.setSectionAttendance(
                sectionAttendanceDetailsRequest.getSectionAttendance());
            attendanceDetails.setEntryTime(
                sectionAttendanceDetailsRequest.getEntryTime() != null
                    ? dateTimeUtil.convertEpochToIso8601Legacy(
                        sectionAttendanceDetailsRequest.getEntryTime())
                    : null);
            attendanceDetails.setExitTime(
                sectionAttendanceDetailsRequest.getExitTime() != null
                    ? dateTimeUtil.convertEpochToIso8601Legacy(
                        sectionAttendanceDetailsRequest.getExitTime())
                    : null);
            attendanceDetails.setNote(sectionAttendanceDetailsRequest.getNote());
            sectionAttendanceDetailsList.add(attendanceDetails);
          }
        });
    return sectionAttendanceDetailsList;
  }

  public void markHoliday(Long attendanceId, MarkHolidayRequest request) {
    var sectionAttendance = sectionAttendanceRepository.findById(attendanceId);
    if (sectionAttendance.isPresent()) {
      var sectionAttendanceData = sectionAttendance.get();
      if (request.getMarkHoliday() == 2) {
        sectionAttendanceData.setStatus(CompletionStatus.HOLIDAY);
      } else {
        sectionAttendance
            .get()
            .setStatus(
                sectionAttendanceData.getAttendanceDetails().isEmpty()
                    ? CompletionStatus.NOTCOMPLETED
                    : CompletionStatus.COMPLETED);
      }
      sectionAttendanceRepository.save(sectionAttendanceData);
    }
  }

  public List<AttendanceResponse> getAttendanceByDate(
      Integer sectionId, Long fromDate, Long toDate) {
    var convertedFromDate = dateTimeUtil.convertEpocToIntegerFormat(fromDate);
    var convertedToDate = dateTimeUtil.convertEpocToIntegerFormat(toDate);

    var sectionAttendanceList =
        sectionAttendanceRepository.findByDates(sectionId, convertedFromDate, convertedToDate);
    return sectionAttendanceList.stream()
        .map(
            s ->
                AttendanceResponse.builder()
                    .section(s.getSection().getName())
                    .date(s.getDateId())
                    .status(s.getStatus())
                    .build())
        .toList();
  }

  public StudentAttendanceResponse getStudentAttendance(
      String orgSlug, String userName, Long fromDate, Long toDate, String sessionType) {
    var org = organizationRepository.findBySlug(orgSlug);
    var convertedFromDate = dateTimeUtil.convertEpocToIntegerFormat(fromDate);
    var convertedToDate = dateTimeUtil.convertEpocToIntegerFormat(toDate);
    User user = userRepository.findByAuthUserId(userName).orElseThrow();
    var student = studentRepository.findByUserId(user.getId());
    List<SectionAttendance> sectionAttendances;
    if (AFTERNOON.equalsIgnoreCase(sessionType)) {
      sectionAttendances =
          sectionAttendanceRepository.findBySectionAndOrgAndDateIdBetweenOrderByDateId(
              student.getSection(), org, convertedFromDate, convertedToDate);
    } else {
      sectionAttendances =
          sectionAttendanceRepository.findBySectionAndOrgAndDateIdBetweenOrderByDateId(
              student.getSection(), org, convertedFromDate, convertedToDate);
    }
    return buildStudentAttendanceResponse(student, sectionAttendances, sessionType);
  }

  private StudentAttendanceResponse buildStudentAttendanceResponse(
      Student student, List<SectionAttendance> sectionAttendances, String sessionType) {
    return StudentAttendanceResponse.builder()
        .section(student.getSection().getName())
        .name(userService.getNameByUserInfo(student.getUserInfo()))
        .grade(student.getSection().getGradeName())
        .response(buildCalenderResponse(student, sectionAttendances, sessionType))
        .build();
  }

  private List<CalenderEventDto.CalenderResponse> buildCalenderResponse(
      Student student, List<SectionAttendance> sectionAttendances, String sessionType) {
    List<CalenderEventDto.CalenderResponse> responses = new ArrayList<>();
    for (SectionAttendance sectionAttendance : sectionAttendances) {
      long epochDate =
          dateTimeUtil.convertDateIdToEpoch(
              Integer.parseInt(String.valueOf(sectionAttendance.getDateId())));
      if (sectionAttendance.getStatus().equals(CompletionStatus.HOLIDAY)) {

        responses.add(
            CalenderEventDto.CalenderResponse.builder()
                .date(epochDate)
                .data(buildHoliday(sectionAttendance))
                .build());
      }
      List<SectionAttendanceDetails> sectionAttendanceDetails =
          sectionAttendance.getAttendanceDetails().stream()
              .filter(
                  sectionAttendanceDetails1 ->
                      sectionAttendanceDetails1.getStudent().equals(student))
              .toList();
      for (SectionAttendanceDetails sectionAttendanceDetail : sectionAttendanceDetails) {
        responses.add(
            CalenderEventDto.CalenderResponse.builder()
                .date(epochDate)
                .data(buildData(sectionAttendanceDetail, sessionType))
                .build());
      }
    }
    return responses;
  }

  private List<CalenderEventDto.Data> buildHoliday(SectionAttendance sectionAttendance) {
    List<CalenderEventDto.Data> holidayList = new ArrayList<>();
    if (sectionAttendance.getStatus().equals(CompletionStatus.HOLIDAY)) {
      CalenderEventDto.Data holidayData =
          CalenderEventDto.Data.builder().type("Holiday").colour("Red").build();
      holidayList.add(holidayData);
    }
    return holidayList;
  }

  private List<CalenderEventDto.Data> buildData(SectionAttendanceDetails s, String sessionType) {
    List<CalenderEventDto.Data> data = new ArrayList<>();
    String type;
    ScheduleInstAttendanceStatus status;
    if (sessionType != null && sessionType.equals(AFTERNOON)) {
      type = s.getAfternoonAttendanceStatus();
    } else {
      type = s.getAttendanceStatus();
    }
    if (type == null || type.isEmpty()) {
      return data;
    }
    status = ScheduleInstAttendanceStatus.fromValue(type);
    var color = getColorForStatus(status);
    CalenderEventDto.Data d = CalenderEventDto.Data.builder().type(type).colour(color).build();
    data.add(d);
    return data;
  }

  private String getColorForStatus(ScheduleInstAttendanceStatus scheduleInstAttendanceStatus) {
    switch (scheduleInstAttendanceStatus) {
      case PRESENT:
        return "Green";
      case ABSENT:
        return "Yellow";
      case HALF_DAY:
        return "Blue";
      case LATE_COMER:
        return "Purple";
      case LEAVE:
        return "Orange";
      case PTM:
        return "Pink";
      case NID:
        return "Grey";
      default:
        return "";
    }
  }

  public List<StudentAttendanceResponse> getAttendanceAfternoonDetails(Long attendanceId) {
    List<SectionAttendanceDetails> sectionAttendanceDetails =
        sectionAttendanceDetailRepository.findBySectionAttendanceId(attendanceId);
    return sectionAttendanceDetails.stream()
        .map(
            s ->
                StudentAttendanceResponse.builder()
                    .id(s.getId())
                    .section(s.getSectionAttendance().getSection().getName())
                    .status(s.getAfternoonAttendanceStatus())
                    .name(
                        s.getStudent().getUserInfo().getFirstName()
                            + " "
                            + s.getStudent().getUserInfo().getLastName())
                    .grade(s.getSectionAttendance().getSection().getGradeName())
                    .authUserId(s.getStudent().getUserInfo().getAuthUserId())
                    .entryTime(
                        s.getEntryTime() != null
                            ? DateTimeUtil.convertIso8601ToEpoch(s.getEntryTime())
                            : null)
                    .exitTime(
                        s.getExitTime() != null
                            ? DateTimeUtil.convertIso8601ToEpoch(s.getExitTime())
                            : null)
                    .note(s.getNote())
                    .userId(s.getStudent().getUserInfo().getId())
                    .build())
        .toList();
  }

  public List<StudentAttendanceResponse> getAttendanceDetails(Long attendanceId) {
    List<SectionAttendanceDetails> sectionAttendanceDetails =
        sectionAttendanceDetailRepository.findBySectionAttendanceId(attendanceId);
    return sectionAttendanceDetails.stream()
        .map(
            s ->
                StudentAttendanceResponse.builder()
                    .section(s.getSectionAttendance().getSection().getName())
                    .status(s.getAttendanceStatus())
                    .name(
                        s.getStudent().getUserInfo().getFirstName()
                            + " "
                            + s.getStudent().getUserInfo().getLastName())
                    .grade(s.getSectionAttendance().getSection().getGradeName())
                    .authUserId(s.getStudent().getUserInfo().getAuthUserId())
                    .entryTime(
                        s.getEntryTime() != null
                            ? DateTimeUtil.convertIso8601ToEpoch(s.getEntryTime())
                            : null)
                    .exitTime(
                        s.getExitTime() != null
                            ? DateTimeUtil.convertIso8601ToEpoch(s.getExitTime())
                            : null)
                    .note(s.getNote())
                    .userId(s.getStudent().getUserInfo().getId())
                    .build())
        .toList();
  }

  public List<AttendanceSummaryResponse> getAttendanceSummary(
      String orgId,
      String teacherAuthId,
      List<String> grades,
      String sectionUuid,
      int limit,
      Long date,
      List<String> boardSlugs,
      String sessionType) {
    var organization = organizationRepository.findBySlug(orgId);
    var teacherUser = userRepository.getUserByAuthUserId(teacherAuthId);
    validateTeacher(teacherUser, orgId);
    List<AttendanceSummary> attendanceSummaries;
    Integer convertedEndDate;
    Integer convertedStartDate;

    if (Objects.isNull(date)) {
      Instant defaultDate = Instant.now().minus(30, ChronoUnit.DAYS);
      convertedStartDate = dateTimeUtil.convertToIntegerFormat(Date.from(Instant.now()));
      convertedEndDate = dateTimeUtil.convertToIntegerFormat(Date.from(defaultDate));
    } else {
      Date selectedDate = dateTimeUtil.convertEpochToTimestamp(date);
      convertedStartDate = dateTimeUtil.convertToIntegerFormat(selectedDate);
      convertedEndDate = convertedStartDate;
    }

    if (Objects.nonNull(sectionUuid)) {
      var section = sectionRepository.findByUuid(UUID.fromString(sectionUuid));
      if (section.isEmpty()) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidSection");
      }
      if (AFTERNOON.equalsIgnoreCase(sessionType)) {
        attendanceSummaries =
            sectionAttendanceDetailRepository.getAfternoonAttendanceSummary(
                organization.getId(),
                convertedStartDate,
                convertedEndDate,
                sectionRepository.getSectionIdsByUuids(List.of(UUID.fromString(sectionUuid))),
                limit);
        return buildAttendanceSummaryList(attendanceSummaries);
      } else {
        attendanceSummaries =
            sectionAttendanceDetailRepository.getAttendanceSummary(
                organization.getId(),
                convertedStartDate,
                convertedEndDate,
                sectionRepository.getSectionIdsByUuids(List.of(UUID.fromString(sectionUuid))),
                limit);
        return buildAttendanceSummaryList(attendanceSummaries);
      }
    }

    if (Objects.nonNull(grades) && !grades.isEmpty() && !boardSlugs.isEmpty()) {
      List<Long> sectionIds =
          getSectionIdsUsingGrades(teacherUser, grades, organization.getSlug(), boardSlugs);
      attendanceSummaries =
          sectionAttendanceDetailRepository.getAttendanceSummary(
              organization.getId(), convertedStartDate, convertedEndDate, sectionIds, limit);
      return buildAttendanceSummaryList(attendanceSummaries);
    }

    List<Long> sectionIds = getSectionIdsWhenNoFiltersAreSelected(teacherUser, orgId);
    attendanceSummaries =
        sectionAttendanceDetailRepository.getAttendanceSummary(
            organization.getId(), convertedStartDate, convertedEndDate, sectionIds, limit);

    return buildAttendanceSummaryList(attendanceSummaries);
  }

  private List<AttendanceSummaryResponse> buildAttendanceSummaryList(
      List<AttendanceSummary> attendanceSummaries) {
    return attendanceSummaries.stream()
        .map(
            s ->
                AttendanceSummaryResponse.builder()
                    .attendanceID(s.getattendanceId())
                    .dateId(s.getDateId())
                    .sectionName(s.getsectionName())
                    .gradeName(s.getgradeName())
                    .absentCount(s.getAbsentCount())
                    .leaveCount(s.getleaveCount())
                    .presentCount(s.getpresentCount())
                    .lateComerCount(s.getLateComerCount())
                    .halfDayCount(s.getHalfDayCount())
                    .ptmCount(s.getPtmCount())
                    .nidCount(s.getNidCount())
                    .status(s.getstatus())
                    .sectionId(s.getsectionId())
                    .sectionUuid(s.getsectionUuid())
                    .build())
        .toList();
  }

  private List<Long> getSectionIdsWhenNoFiltersAreSelected(User teacherUser, String orgId) {
    if (AuthUtil.isOrgAdmin(teacherUser)) {
      var sections = sectionService.getAllSections(orgId, false);
      return sections.stream().map(SectionEntityDto.Response::id).toList();
    }
    return teacherSubjectsRepository.findByTeacher(teacherUser.getAuthUserId()).stream()
        .map(TeacherSubjects::getSection)
        .map(Section::getId)
        .toList();
  }

  private List<Long> getSectionIdsUsingGrades(
      User teacherUser, List<String> gradeSlugs, String orgSlug, List<String> boardSlugs) {
    if (AuthUtil.isOrgAdmin(teacherUser)) {
      return sectionRepository
          .findAllByOrganizationAndBoardSlugInAndGradeSlugIn(orgSlug, boardSlugs, gradeSlugs)
          .stream()
          .map(Section::getId)
          .toList();
    }
    return teacherSubjectsRepository.findByTeacher(teacherUser.getAuthUserId()).stream()
        .map(TeacherSubjects::getSection)
        .filter(section -> gradeSlugs.contains(section.getGradeSlug()))
        .map(Section::getId)
        .toList();
  }

  private void validateTeacher(User teacherUser, String orgId) {
    if (isNull(teacherUser) || !orgId.equals(teacherUser.getOrganization())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Teacher.NotFound");
    }
  }

  public List<GenericMetricResponse> getSectionWiseAttendanceReport(
      String org, int date, String gradeSlug, String sessionType) {

    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    var dateId = LocalDate.now().minusDays(date).format(formatter);

    List<AttendanceSummary> attendanceReport;
    if (userRoleHelper.isStaff(authService.getUserDetails())) {
      Long academyId =
          userRepository.getCoOrdinatorAndAcademyIdByUserId(authService.getUserDetails().getId());

      List<Student> students = studentRepository.findByAcademyId(academyId);
      List<String> gradeSlugs =
          students.stream().map(s -> s.getSection().getGradeSlug()).distinct().toList();
      if (AFTERNOON.equalsIgnoreCase(sessionType)) {
        attendanceReport =
            sectionAttendanceDetailRepository.getSectionWiseAfternoonAttendanceReportForCoordinator(
                org, Integer.valueOf(dateId), students.stream().map(Student::getId).toList());
      } else {
        attendanceReport =
            sectionAttendanceDetailRepository.getSectionWiseAttendanceReportForCoordinator(
                org, Integer.valueOf(dateId), students.stream().map(Student::getId).toList());
      }
    } else {
      if (AFTERNOON.equalsIgnoreCase(sessionType)) {
        attendanceReport =
            sectionAttendanceDetailRepository.getSectionWiseAfternoonAttendanceReport(
                org, Integer.valueOf(dateId), gradeSlug);
      } else {
        attendanceReport =
            sectionAttendanceDetailRepository.getSectionWiseAttendanceReport(
                org, Integer.valueOf(dateId), gradeSlug);
      }
    }

    var sections = sectionService.getSectionsByGrade(org, gradeSlug);

    var sectionIds = attendanceReport.stream().map(AttendanceSummary::getsectionId).toList();

    for (var section : sections) {
      Map<String, Object> dataMap = new HashMap<>();

      if (sectionIds.contains(section.id())) {
        AttendanceSummary summary =
            attendanceReport.stream()
                .filter(r -> r.getsectionId().equals(section.id()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(summary)) {
          throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.CannotFindSection");
        }

        dataMap.put("section_name", summary.getsectionName());
        dataMap.put("uuid", summary.getsectionUuid());
        dataMap.put(ATTENDEES, summary.getpresentCount());
        dataMap.put(ABSENTEES, summary.getAbsentCount());
        dataMap.put(LEAVES, summary.getleaveCount());
        dataMap.put(LATE_COMER, summary.getLateComerCount());
        dataMap.put(HALF_DAY, summary.getHalfDayCount());
        dataMap.put(PTM, summary.getPtmCount());
        dataMap.put(NID, summary.getNidCount());
        dataMap.put(
            TOTAL_STRENGTH,
            summary.getpresentCount()
                + summary.getAbsentCount()
                + summary.getLateComerCount()
                + summary.getHalfDayCount()
                + summary.getleaveCount()
                + summary.getPtmCount()
                + summary.getNidCount());
      } else {

        var sectionEntity = sectionService.findSectionById(section.id());
        var students = studentRepository.getStudentsBySection(sectionEntity);
        dataMap.put("section_name", section.name());
        dataMap.put("uuid", section.uuid().toString());
        dataMap.put(ATTENDEES, 0);
        dataMap.put(ABSENTEES, 0);
        dataMap.put(LEAVES, 0);
        dataMap.put(LATE_COMER, 0);
        dataMap.put(HALF_DAY, 0);
        dataMap.put(PTM, 0);
        dataMap.put(NID, 0);
        dataMap.put(TOTAL_STRENGTH, students.size());
      }
      genericMetricResponses.add(
          GenericMetricResponse.builder()
              .date(getEpochFromStringDate(LocalDate.now().minusDays(date).toString()))
              .data(dataMap)
              .build());
    }
    return genericMetricResponses;
  }

  public List<GenericMetricResponse> getGradeWiseAttendanceReport(
      String org, Integer date, List<String> gradeList, String sessionType) {

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    var dateId = LocalDate.now().minusDays(date).format(formatter);

    List<AttendanceSummary> attendanceReports;
    if (AFTERNOON.equalsIgnoreCase(sessionType)) {
      attendanceReports =
          sectionAttendanceDetailRepository.getGradeWiseAfternoonAttendanceReport(
              org, Integer.valueOf(dateId), gradeList);
    } else {
      attendanceReports =
          sectionAttendanceDetailRepository.getGradeWiseAttendanceReport(
              org, Integer.valueOf(dateId), gradeList);
    }
    return getGenericMetricResponse(attendanceReports, gradeList, date, org);
  }

  private List<GenericMetricResponse> getGenericMetricResponse(
      List<AttendanceSummary> attendanceReports, List<String> gradeList, Integer date, String org) {

    var gradesSlugs = attendanceReports.stream().map(AttendanceSummary::getGradeSlug).toList();

    var gradesMap = strapiContentHelper.getGradesMap();

    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();

    for (var grade : gradeList) {

      Map<String, Object> dataMap = new HashMap<>();

      if (gradesSlugs.contains(grade)) {
        var summary =
            attendanceReports.stream()
                .filter(r -> r.getGradeSlug().equals(grade))
                .findAny()
                .orElse(null);
        if (Objects.isNull(summary)) {
          throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.GradeNotFound");
        }
        dataMap.put("grade_slug", grade);
        dataMap.put("grade_name", summary.getgradeName());
        dataMap.put(ATTENDEES, summary.getpresentCount());
        dataMap.put(ABSENTEES, summary.getAbsentCount());
        dataMap.put(LEAVES, summary.getleaveCount());
        dataMap.put(LATE_COMER, summary.getLateComerCount());
        dataMap.put(HALF_DAY, summary.getHalfDayCount());
        dataMap.put(PTM, summary.getPtmCount());
        dataMap.put(NID, summary.getNidCount());
        dataMap.put(
            TOTAL_STRENGTH,
            summary.getpresentCount()
                + summary.getAbsentCount()
                + summary.getLateComerCount()
                + summary.getHalfDayCount()
                + summary.getleaveCount()
                + summary.getPtmCount()
                + summary.getNidCount());
      } else {
        var students = studentRepository.findByGradeAndOrg(Collections.singletonList(grade), org);

        dataMap.put("grade_slug", grade);
        dataMap.put("grade_name", gradesMap.get(grade).getName());
        dataMap.put(ATTENDEES, 0);
        dataMap.put(ABSENTEES, 0);
        dataMap.put(LEAVES, 0);
        dataMap.put(TOTAL_STRENGTH, students.size());
      }
      genericMetricResponses.add(
          GenericMetricResponse.builder()
              .date(getEpochFromStringDate(LocalDate.now().minusDays(date).toString()))
              .data(dataMap)
              .build());
    }
    return genericMetricResponses;
  }

  public List<GenericMetricResponse> getStudentAttendanceReport(
      String orgSlug,
      List<Long> boardIds,
      List<String> gradeSlugs,
      List<Long> sectionIds,
      String fromDate,
      String toDate,
      String sessionType) {
    List<GenericMetricResponse> responseList = new ArrayList<>();
    var convertedFromDate = dateTimeUtil.convertEpocToIntegerFormat(Long.valueOf(fromDate));
    var convertedToDate = dateTimeUtil.convertEpocToIntegerFormat(Long.valueOf(toDate));

    if (AFTERNOON.equalsIgnoreCase(sessionType)) {
      var attendanceReport =
          sectionAttendanceRepository.getStudentsAfternoonAttendanceReport(
              orgSlug, gradeSlugs, sectionIds, boardIds, convertedFromDate, convertedToDate);
      responseList.add(
          GenericMetricResponse.builder()
              .data(buildStudentAttendanceReportReponse(attendanceReport))
              .build());
    } else {
      var attendanceReport =
          sectionAttendanceRepository.getStudentsAttendanceReport(
              orgSlug, gradeSlugs, sectionIds, boardIds, convertedFromDate, convertedToDate);
      responseList.add(
          GenericMetricResponse.builder()
              .data(buildStudentAttendanceReportReponse(attendanceReport))
              .build());
    }
    return responseList;
  }

  private Map<String, Object> buildStudentAttendanceReportReponse(
      List<StudentsAttendanceReport> studentAttendanceReports) {
    List<StudentAttendanceReportDto.Response> responseList = new ArrayList<>();
    HashMap<String, Object> data = new HashMap<>();
    if (studentAttendanceReports.isEmpty()) {
      return data;
    }
    studentAttendanceReports.forEach(
        student -> {
          var user = validationUtils.isValidUser(student.getAuthId());
          Optional<StudentAttributeValueModel> admissionNo =
              reportCardService.getStudentAttributeValue(user.getStudentInfo(), ADMISSION_NO);
          responseList.add(
              StudentAttendanceReportDto.Response.builder()
                  .name(student.getFullName())
                  .admissionNumber(student.getRollNumber())
                  .authId(student.getAuthId())
                  .userName(student.getUserName())
                  .SectionName(student.getSectionName())
                  .RollNumber(student.getRollNumber())
                  .GradeName(student.getGradeName())
                  .AbsentDays(student.getAbsentDays())
                  .PresentDays(student.getPresentDays())
                  .HalfDays(student.getHalfDayDays())
                  .LeaveDays(student.getLeaveDays())
                  .LateComerDays(student.getLateComerDays())
                  .PtmDays(student.getPtmDays())
                  .TotalWorkingDays(student.getTotalWorkingDays())
                  .classRollNumber(getClassRollNumber(student))
                  .build());
        });
    responseList.sort(
        Comparator.comparing(
            StudentAttendanceReportDto.Response::classRollNumber,
            Comparator.nullsLast(Comparator.naturalOrder())));
    data.put("students_attendance_report", responseList);
    return data;
  }

  public List<GenericMetricResponse> getOrgAttendanceSummary(
      String orgSlug, Integer date, String session, String status) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    var dateId = Integer.valueOf(LocalDate.now().minusDays(date).format(formatter));
    List<OrgLevelAttendance> orgLevelAttendance =
        sectionAttendanceDetailRepository.getOrgLevelAttendance(orgSlug, dateId, session, status);
    var grades =
        strapiService.getAllGrades().stream()
            .sorted(Comparator.comparing(Grade::getOrder))
            .toList();

    Map<String, Integer> gradeOrderMap = new HashMap<>();
    for (int i = 0; i < grades.size(); i++) {
      gradeOrderMap.put(grades.get(i).getSlug(), i);
    }

    orgLevelAttendance.sort(
        Comparator.comparingInt(
                (OrgLevelAttendance o) ->
                    gradeOrderMap.getOrDefault(o.getGradeSlug(), Integer.MAX_VALUE))
            .thenComparingInt(
                o -> {
                  String rollNumber = o.getClassRollNumber();
                  try {
                    return (rollNumber != null && !rollNumber.trim().isEmpty())
                        ? Integer.parseInt(rollNumber.trim())
                        : Integer.MAX_VALUE;
                  } catch (NumberFormatException e) {
                    return Integer.MAX_VALUE;
                  }
                }));

    HashMap<String, Object> map = new HashMap<>();
    map.put("count", orgLevelAttendance.size());
    map.put("students", orgLevelAttendance);
    GenericMetricResponse result = new GenericMetricResponse();
    result.setData(map);
    return List.of(result);
  }

  public List<AttendanceSummaryResponse> findStudentByGlobalSearch(
      String orgId, String searchName, Integer timePeriod, Long teacherId) {
    Integer dateId = null;
    if (Objects.nonNull(timePeriod)) {
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
      dateId = Integer.valueOf(LocalDate.now().minusDays(timePeriod).format(formatter));
    }
    List<Long> sectionIdByMatchingStudent =
        studentRepository.findSectionIdByMatchingStudent(orgId, searchName + "%");
    if (teacherId != null) {
      List<Long> teacherSections =
          teacherSectionRepository.getTeacherSectionsByTeacherAndDeletedAtIsNull(teacherId).stream()
              .map(teacherSection -> teacherSection.getSection().getId())
              .toList();
      sectionIdByMatchingStudent.retainAll(teacherSections);
    }
    List<AttendanceSummary> studentByOrgIdAndName =
        sectionAttendanceRepository.findSectionByDateAndSectionIds(
            dateId, sectionIdByMatchingStudent);
    List<AttendanceSummaryResponse> attendanceSearchUserResponses = new ArrayList<>();
    studentByOrgIdAndName.forEach(
        student -> {
          AttendanceSummaryResponse attendanceSearchUserResponse = new AttendanceSummaryResponse();
          attendanceSearchUserResponse.setSectionId(student.getsectionId());
          attendanceSearchUserResponse.setAttendanceID(student.getattendanceId());
          attendanceSearchUserResponse.setSectionName(student.getsectionName());
          attendanceSearchUserResponse.setGradeName(student.getgradeName());
          attendanceSearchUserResponse.setPresentCount(student.getpresentCount());
          attendanceSearchUserResponse.setAbsentCount(student.getAbsentCount());
          attendanceSearchUserResponse.setLateComerCount(student.getLateComerCount());
          attendanceSearchUserResponse.setHalfDayCount(student.getHalfDayCount());
          attendanceSearchUserResponse.setLeaveCount(student.getleaveCount());
          attendanceSearchUserResponse.setPtmCount(student.getPtmCount());
          attendanceSearchUserResponse.setNidCount(student.getNidCount());
          attendanceSearchUserResponse.setSectionUuid(student.getsectionUuid());
          attendanceSearchUserResponse.setGradeName(student.getgradeName());
          attendanceSearchUserResponse.setSectionName(student.getsectionName());
          attendanceSearchUserResponse.setStatus(
              CompletionStatus.values()[student.getisHoliday()].toString());
          attendanceSearchUserResponse.setIsHoliday(student.getisHoliday());
          attendanceSearchUserResponse.setDateId(
              DateTimeUtil.convertDateIdToEpoch(student.getDateId().intValue()));
          attendanceSearchUserResponses.add(attendanceSearchUserResponse);
        });
    return attendanceSearchUserResponses;
  }

  private CompletionStatus getCompletionStatus(String status) {
    if (status == null || status.isEmpty()) {
      return null;
    }
    if (status.equalsIgnoreCase("0")) {
      return CompletionStatus.COMPLETED;
    } else if (status.equalsIgnoreCase("1")) {
      return CompletionStatus.NOTCOMPLETED;
    } else if (status.equalsIgnoreCase("2")) {
      return CompletionStatus.HOLIDAY;
    } else {
      return null;
    }
  }

  public List<AttendanceSummaryResponse> getAttendanceReport(String orgSlug) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    Integer dateId = Integer.valueOf(LocalDate.now().minusDays(0).format(formatter));

    Organization org = organizationRepository.findBySlug(orgSlug);
    if (org == null) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "Organization not found: ");
    }

    List<AttendanceSummary> summaries =
        sectionAttendanceDetailRepository.getAttendanceSummaryBetweenDates(org.getId(), dateId);

    List<AttendanceSummaryResponse> responseList = new ArrayList<>();
    for (AttendanceSummary summary : summaries) {
      Long presentCount = summary.getpresentCount() != null ? summary.getpresentCount() : 0L;
      Long absentCount = summary.getAbsentCount() != null ? summary.getAbsentCount() : 0L;
      Long lateComerCount = summary.getLateComerCount() != null ? summary.getLateComerCount() : 0L;
      Long halfDayCount = summary.getHalfDayCount() != null ? summary.getHalfDayCount() : 0L;
      Long leaveCount = summary.getHalfDayCount() != null ? summary.getleaveCount() : 0L;
      Long ptmCount = summary.getPtmCount() != null ? summary.getPtmCount() : 0L;
      Long nidCount = summary.getNidCount() != null ? summary.getNidCount() : 0L;

      responseList.add(
          AttendanceSummaryResponse.builder()
              .dateId(DateTimeUtil.convertDateIdToEpoch(dateId))
              .presentCount(presentCount)
              .absentCount(absentCount)
              .lateComerCount(lateComerCount)
              .halfDayCount(halfDayCount)
              .leaveCount(leaveCount)
              .ptmCount(ptmCount)
              .nidCount(nidCount)
              .build());
    }

    return responseList;
  }

  public List<StudentAttendanceSummaryDto.Response> getStudentsAttendanceSummaryBySection(
      String sectionUuId, Month month, Integer year) {
    var section = validationUtils.findSectionByUuid(sectionUuId);

    LocalDateTime fromDate = LocalDateTime.of(year, month.ordinal() + 1, 1, 0, 0);
    LocalDateTime toDate =
        LocalDateTime.of(
            year,
            month.ordinal() + 1,
            YearMonth.of(year, month.ordinal() + 1).lengthOfMonth(),
            23,
            59,
            59);
    Integer academicYear = (month.ordinal() + 1) >= 4 ? year : year - 1;

    LocalDateTime academicLocalDateTime =
        LocalDateTime.of(academicYear, java.time.Month.APRIL, 7, 0, 0);

    Integer convertedFromDate =
        dateTimeUtil.convertToIntegerFormat(java.sql.Date.valueOf(fromDate.toLocalDate()));
    Integer convertedToDate =
        dateTimeUtil.convertToIntegerFormat(java.sql.Date.valueOf(toDate.toLocalDate()));

    Integer academicStartDateId =
        dateTimeUtil.convertToIntegerFormat(
            java.sql.Date.valueOf(LocalDate.of(academicYear, java.time.Month.APRIL, 7)));

    List<SectionAttendance> sectionAttendancesList =
        sectionAttendanceRepository.findBySectionIdAndDateIdBetween(
            section.getId(), academicStartDateId, convertedToDate);
    List<Student> students = studentRepository.getStudentsBySection(section);

    Map<Integer, CompletionStatus> dateIds =
        sectionAttendancesList.stream()
            .collect(Collectors.toMap(SectionAttendance::getDateId, SectionAttendance::getStatus));

    Integer totalWorkingDays = 0;
    Integer totalWorkingDaysInMonth = 0;
    for (LocalDateTime dateTime = fromDate;
        !dateTime.isAfter(toDate);
        dateTime = dateTime.plusDays(1)) {
      Integer startDateId =
          dateTimeUtil.convertToIntegerFormat(java.sql.Date.valueOf(dateTime.toLocalDate()));
      if (dateIds.get(startDateId) != null
          && dateIds.get(startDateId).equals(CompletionStatus.COMPLETED)
          && !dateTime.toLocalDate().getDayOfWeek().equals(DayOfWeek.SUNDAY)) {
        totalWorkingDaysInMonth++;
      }
    }

    for (LocalDateTime dateTime = academicLocalDateTime;
        !dateTime.isAfter(toDate);
        dateTime = dateTime.plusDays(1)) {
      Integer startDateId =
          dateTimeUtil.convertToIntegerFormat(java.sql.Date.valueOf(dateTime.toLocalDate()));
      dateIds.putIfAbsent(startDateId, CompletionStatus.NOTCOMPLETED);
      if (dateIds.get(startDateId).equals(CompletionStatus.COMPLETED)) totalWorkingDays++;
    }

    Map<String, SectionAttendanceDetails> studentDateMap = new HashMap<>();
    Map<Long, Integer> totalStudentAttendanceMap = new HashMap<>();
    for (SectionAttendance sectionAttendance : sectionAttendancesList) {
      int dateId = sectionAttendance.getDateId();
      for (SectionAttendanceDetails detail : sectionAttendance.getAttendanceDetails()) {
        Long studentId = detail.getStudent().getId();
        String key = studentId + "_" + dateId;
        studentDateMap.put(key, detail);
        totalStudentAttendanceMap.putIfAbsent(studentId, 0);
        if (detail.getAttendanceStatus().equalsIgnoreCase("present")) {
          Integer attendanceCount = totalStudentAttendanceMap.get(studentId);
          totalStudentAttendanceMap.put(studentId, ++attendanceCount);
        }
      }
    }

    List<StudentAttendanceSummaryDto.Response> responses = new ArrayList<>();

    for (Student student :
        students.stream().filter(s -> s.getUserInfo().getDeletedAt() == null).toList()) {
      int presentCount = 0;
      int absentCount = 0;
      int leaveCount = 0;
      int lateComerCount = 0;
      int halfDayCount = 0;
      int ptmCount = 0;
      int nidCount = 0;
      List<StudentAttendanceSummaryDto.AttendanceDetail> attendanceDetails = new ArrayList<>();
      for (Integer dateId =
              academicStartDateId > convertedFromDate ? academicStartDateId : convertedFromDate;
          dateId <= convertedToDate;
          dateId++) {
        String key = student.getId() + "_" + dateId;
        SectionAttendanceDetails studentSectionAttendanceDetails = studentDateMap.get(key);
        String status;
        if (studentSectionAttendanceDetails != null) {
          status = studentSectionAttendanceDetails.getAttendanceStatus();
          if ("present".equalsIgnoreCase(status)) {
            status = "P";
            presentCount++;
          } else if ("absent".equalsIgnoreCase(status)) {
            status = "A";
            absentCount++;
          } else if ("leave".equalsIgnoreCase(status)) {
            status = "L";
            leaveCount++;
          } else if ("late_comer".equalsIgnoreCase(status)) {
            status = "LC";
            lateComerCount++;
          } else if ("half_day".equalsIgnoreCase(status)) {
            status = "HDL";
            halfDayCount++;
          } else if ("ptm".equalsIgnoreCase(status)) {
            status = "PTM";
            ptmCount++;
          } else if ("nid".equalsIgnoreCase(status)) {
            status = "NID";
            nidCount++;
          } else if ("holiday".equalsIgnoreCase(status)) {
            status = "H";
          } else if (dateTimeUtil
              .convertEpochToIso8601(DateTimeUtil.convertDateIdToEpoch(dateId))
              .getDayOfWeek()
              .equals(DayOfWeek.SUNDAY)) {
            status = "-";
          }
        } else {
          status = dateIds.get(dateId).toString();
          if (status == null
              || status.equalsIgnoreCase(CompletionStatus.NOTCOMPLETED.toString())
              || status.equalsIgnoreCase(CompletionStatus.COMPLETED.toString())) {
            status = "";
          } else if ("holiday".equalsIgnoreCase(status)) {
            status = "H";
          } else if (dateTimeUtil
              .convertEpochToIso8601(DateTimeUtil.convertDateIdToEpoch(dateId))
              .getDayOfWeek()
              .equals(DayOfWeek.SUNDAY)) {
            status = "-";
          }
        }
        attendanceDetails.add(
            StudentAttendanceSummaryDto.AttendanceDetail.builder()
                .date(DateTimeUtil.convertDateIdToEpoch(dateId))
                .status(status)
                .build());
      }
      Integer attendanceCount = totalStudentAttendanceMap.get(student.getId());
      attendanceCount = attendanceCount == null ? 0 : attendanceCount;
      Double totalAttendancePercentage = 0.0;
      if (totalWorkingDays > 0) {
        totalAttendancePercentage = (attendanceCount * 100.0) / Double.valueOf(totalWorkingDays);
      }
      StudentAttendanceSummaryDto.Response response =
          StudentAttendanceSummaryDto.Response.builder()
              .studentId(student.getId())
              .studentName(
                  student.getUserInfo().getFirstName() + " " + student.getUserInfo().getLastName())
              .admissionNo(student.getRollNumber())
              .classRollNumber(student.getClassRollNumber())
              .presentCount(presentCount)
              .absentCount(absentCount)
              .lateComerCount(lateComerCount)
              .halfDayCount(halfDayCount)
              .leaveCount(leaveCount)
              .ptmCount(ptmCount)
              .nidCount(nidCount)
              .totalAttendance(totalStudentAttendanceMap.get(student.getId()))
              .monthlyWorkingDays(totalWorkingDaysInMonth)
              .totalWorkingDays(totalWorkingDays)
              .attendanceDetails(attendanceDetails)
              .totalAttendancePercentage(totalAttendancePercentage)
              .build();
      responses.add(response);
    }
    return responses;
  }

  private Long getClassRollNumber(StudentsAttendanceReport student) {
    try {
      Long classRollNumber = student.getClassRollNumber();
      return classRollNumber != null ? classRollNumber : 0L;
    } catch (Exception e) {
      return 0L;
    }
  }
}
