package com.wexl.pallavi.reports;

import static java.lang.String.format;

import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.pallavi.dto.ReportCardDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class FirstTerm3To5Report extends PallaviBaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final UserService userService;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(
      User user, Organization org, com.wexl.retail.offlinetest.dto.ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(
      User user, Organization org, com.wexl.retail.offlinetest.dto.ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("3rd-5th-term1-report.xml");
  }

  public ReportCardDto.Body buildBody(User user, String orgSlug) {
    var student = user.getStudentInfo();
    var guardians = student.getGuardians();
    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    var classTeacher = student.getSection().getClassTeacher();
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var tableMarks = buildTableMarks(student);

    return ReportCardDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(null))
        .className(student.getSection().getName())
        .rollNumber(student.getRollNumber())
        .fathersName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .mothersName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .teacherName(
            Objects.nonNull(classTeacher)
                ? userService.getNameByUserInfo(classTeacher.getUserInfo())
                : null)
        .firstTable(buildFirstTable(tableMarks.firstTableMarks(), student))
        .secondTable(buildSecondTable(tableMarks.secondTableMarks()))
        .thirdTable(buildThirdTable(tableMarks.thirdTableMarks()))
        .attendance(buildAttendance(student.getId()))
        .gradeTable(ReportCardDto.GradeTable.builder().title("Grade Scale").build())
        .build();
  }

  private ReportCardDto.ThirdTableMarks buildThirdTable(
      List<ReportCardDto.ThirdTableMarks> thirdTableMarks) {
    var subjectNames =
        thirdTableMarks.stream()
            .map(ReportCardDto.ThirdTableMarks::subjectName)
            .collect(Collectors.toList());
    if (subjectNames.isEmpty()) {
      return ReportCardDto.ThirdTableMarks.builder().build();
    }
    return ReportCardDto.ThirdTableMarks.builder()
        .title("PART II : Co Scholastic Areas[on a 3-point (A-C) grading scale]")
        .marks(buildTableMark(thirdTableMarks))
        .build();
  }

  private List<ReportCardDto.ThirdTableMark> buildTableMark(
      List<ReportCardDto.ThirdTableMarks> thirdTableMarks) {
    if (thirdTableMarks.size() > 0) {
      return thirdTableMarks.stream()
          .map(
              mark ->
                  ReportCardDto.ThirdTableMark.builder()
                      .subjectName(mark.subjectName())
                      .term1Grade(mark.term1Grade())
                      .build())
          .collect(Collectors.toList());
    } else return Collections.singletonList(ReportCardDto.ThirdTableMark.builder().build());
  }

  private ReportCardDto.TableMarks buildTableMarks(Student student) {

    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            Collections.singletonList("t1"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var scholasticMandatoryData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var scholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();

    var coScholasticData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .toList();

    var sortedData =
        sortTable(
            buildTableMarks(scholasticMandatoryData),
            buildTableMarks(scholasticOptionalData),
            buildTableMarks(coScholasticData));
    return ReportCardDto.TableMarks.builder()
        .firstTableMarks(sortedData.firstTableMarks())
        .secondTableMarks(sortedData.secondTableMarks())
        .thirdTableMarks(sortedData.thirdTableMarks())
        .build();
  }

  public List<ReportCardDto.Marks> buildTableMarks(List<LowerGradeReportCardData> reportCardData) {
    List<ReportCardDto.Marks> marksList = new ArrayList<>();
    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));
    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var pt1 = getMarks("pa1", scholasticData);
          var ma1 = getMarks("ma1", scholasticData);
          var cwHw = getMarks("cwhw", scholasticData);
          var se1 = getMarks("se1", scholasticData);
          var hye = getMarks("hye", scholasticData);

          var term1Total = sumMarks(pt1, ma1, cwHw, se1, hye) * 2;

          var termMaxMarks = 100d;

          long term1TotalMarks = Math.round(term1Total);
          String grade = calculateGrade((double) term1TotalMarks, termMaxMarks);
          var overallMarksDouble = decimalFormat((double) term1TotalMarks);
          long term1TotalLong = Math.round(Float.parseFloat(overallMarksDouble));
          marksList.add(
              ReportCardDto.Marks.builder()
                  .subject(subject)
                  .pt1(String.valueOf(pt1))
                  .ma1(String.valueOf(ma1))
                  .cwHw1(String.valueOf(cwHw))
                  .se1(String.valueOf(se1))
                  .hye(String.valueOf(hye))
                  .seqNo(scholasticData.get(0).getSeqNo())
                  .term1total(term1TotalLong)
                  .totalMarks(termMaxMarks)
                  .term1Grade(grade)
                  .build());
        });
    return marksList;
  }

  private String calculateGrade(Double marks, Double totalMarks) {
    return marks == null || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate("8point", BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private Double getMarks(String assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    double average =
        subjectData.stream()
            .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
            .map(LowerGradeReportCardData::getMarks)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .average()
            .orElse(0.0);

    return Double.parseDouble(String.format("%.1f", average));
  }

  private Double sumMarks(Double... marks) {
    return Arrays.stream(marks).filter(Objects::nonNull).mapToDouble(Double::doubleValue).sum();
  }

  private ReportCardDto.FirstTable buildFirstTable(
      List<ReportCardDto.Marks> firstTableMarks, Student student) {
    var section = student.getSection();

    var reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlug(
            section.getOrganization(), section.getBoardSlug(), section.getGradeSlug());
    var configDetails =
        reportCardConfigs.getFirst().getReportCardConfigDetails().stream()
            .sorted(Comparator.comparing(ReportCardConfigDetail::getSeqNo))
            .toList();

    var termTotal =
        firstTableMarks.stream()
            .map(ReportCardDto.Marks::term1total)
            .filter(Objects::nonNull)
            .mapToDouble(s -> s)
            .sum();

    var total =
        firstTableMarks.stream()
            .map(ReportCardDto.Marks::totalMarks)
            .filter(Objects::nonNull)
            .mapToDouble(s -> s)
            .sum();
    var percentage = (termTotal / total * 100);
    var doubleValue = decimalFormat(percentage);

    return ReportCardDto.FirstTable.builder()
        .title("PART-I: SCHOLASTIC AREAS")
        .column1(constructColumn(configDetails.getFirst()))
        .column2(constructColumn(configDetails.get(1)))
        .column3(constructColumn(configDetails.get(2)))
        .column4(constructColumn(configDetails.get(3)))
        .column5(constructColumn(configDetails.get(4)))
        .marks(firstTableMarks)
        .percentageGrade(
            ReportCardDto.PercentageGrade.builder()
                .term1total(Double.valueOf(doubleValue))
                .term1Grade(calculateGrade(termTotal, total))
                .build())
        .totals(buildTotals(firstTableMarks))
        .build();
  }

  private String decimalFormat(Double value) {
    return String.format("%.2f", value);
  }

  private String constructColumn(ReportCardConfigDetail configDetail) {
    return Objects.isNull(configDetail)
        ? null
        : format(
            "%s (%s)", configDetail.getTermAssessment().getName(), configDetail.getWeightage());
  }

  private ReportCardDto.Totals buildTotals(List<ReportCardDto.Marks> firstTableMarks) {
    var totalMarksScored =
        firstTableMarks.stream()
            .map(ReportCardDto.Marks::term1total)
            .filter(Objects::nonNull)
            .mapToDouble(Long::longValue)
            .average();

    String grade =
        totalMarksScored.isEmpty()
            ? "N/A"
            : pointScaleEvaluator.evaluate(
                "8point", BigDecimal.valueOf(totalMarksScored.getAsDouble()));

    String overallPercentage = "N/A".equals(grade) ? "0.0" : grade;

    return ReportCardDto.Totals.builder().overallPercentage(overallPercentage).build();
  }

  private ReportCardDto.SecondTable buildSecondTable(List<ReportCardDto.ThirdTableMarks> marks) {
    return ReportCardDto.SecondTable.builder().marks(marks).build();
  }

  private ReportCardDto.Attendance buildAttendance(Long studentId) {
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendanceByStudentId(
            studentId);
    if (studentAttendance.isEmpty()) {
      return ReportCardDto.Attendance.builder().build();
    }

    var testDefinition = studentAttendance.get().getOfflineTestDefinition();
    if (testDefinition == null) {
      throw new IllegalArgumentException("Test definition not found for studentId: " + studentId);
    }
    String totalAttendanceDays = testDefinition.getTotalAttendanceDays();
    Long daysPresent = studentAttendance.get().getPresentDays();
    if (totalAttendanceDays == null || daysPresent == null) {
      return ReportCardDto.Attendance.builder().build();
    }

    Double attendancePercentage = null;
    double totalDays = Double.parseDouble(totalAttendanceDays);
    if (totalDays > 0.0) {
      attendancePercentage = (double) Math.round((daysPresent.doubleValue() / totalDays) * 100);
    }
    return ReportCardDto.Attendance.builder()
        .workingDays(Long.valueOf(totalAttendanceDays))
        .daysPresent(daysPresent)
        .attendancePercentage(attendancePercentage)
        .remarks(studentAttendance.get().getRemarks())
        .build();
  }

  private ReportCardDto.TableMarks sortTable(
      List<ReportCardDto.Marks> firstTableMarks,
      List<ReportCardDto.Marks> secondTableMarks,
      List<ReportCardDto.Marks> thirdTableMarks) {
    List<ReportCardDto.Marks> firstTable = new ArrayList<>();
    var sortedFirstTable =
        firstTableMarks.stream()
            .sorted(Comparator.comparingLong(ReportCardDto.Marks::seqNo))
            .toList();

    for (int i = 1; i <= sortedFirstTable.size(); i++) {
      ReportCardDto.Marks mark = sortedFirstTable.get(i - 1);
      firstTable.add(
          ReportCardDto.Marks.builder()
              .sno((long) i)
              .pt1(mark.pt1())
              .ma1(mark.ma1())
              .cwHw1(mark.cwHw1())
              .se1(mark.se1())
              .hye(mark.hye())
              .subject(mark.subject())
              .term1total(mark.term1total())
              .term1Grade(mark.term1Grade())
              .totalMarks(mark.totalMarks())
              .build());
    }

    var termMaxMarks = 50d;
    var secondTable =
        secondTableMarks.stream()
            .sorted(Comparator.comparingLong(ReportCardDto.Marks::seqNo))
            .map(
                mark ->
                    ReportCardDto.ThirdTableMarks.builder()
                        .subjectName(mark.subject())
                        .term1Marks(mark.term1total() / 2)
                        .term1Grade(
                            calculateGrade(Double.valueOf(mark.term1total() / 2), termMaxMarks))
                        .build())
            .toList();

    var thirdTable =
        thirdTableMarks.stream()
            .sorted(Comparator.comparingLong(ReportCardDto.Marks::seqNo))
            .map(
                mark ->
                    ReportCardDto.ThirdTableMarks.builder()
                        .term1Grade(
                            calculatePointGrade(BigDecimal.valueOf(mark.term1total()), "3point"))
                        .subjectName(mark.subject())
                        .build())
            .toList();

    return ReportCardDto.TableMarks.builder()
        .firstTableMarks(firstTable)
        .secondTableMarks(secondTable)
        .thirdTableMarks(thirdTable)
        .build();
  }

  private String calculatePointGrade(BigDecimal marks, String gradeScaleSlug) {
    return marks == null ? null : pointScaleEvaluator.evaluate(gradeScaleSlug, marks);
  }
}
