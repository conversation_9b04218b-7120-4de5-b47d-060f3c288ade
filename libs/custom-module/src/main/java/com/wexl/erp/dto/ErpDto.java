package com.wexl.erp.dto;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

public class ErpDto {
  @Builder
  public record ErpTeacherResponse(
      int id,
      String teacherName,
      @NotNull String teacherCode,
      String schoolId,
      String email,
      String phone,
      String gender,
      String orgSlug) {}

  @Builder
  public record ErpStudentResponse(
      int id,
      String firstName,
      String lastName,
      @NotNull String studentCode,
      String schoolId,
      String email,
      String phone,
      String branchCode,
      String branchName,
      String rollNumber,
      String grade,
      String sectionName,
      String sectionUuid,
      String gender,
      String fatherName,
      String fatherPhone,
      String motherName,
      String motherPhone,
      String fatherEmail,
      String motherEmail,
      String orgSlug) {}

  @Builder
  public record ErpEntityChange(
      String changeType,
      String commitId,
      Integer id,
      String dateTime,
      String employeeCode,
      String type,
      ErpTeacherResponse teacherResponse,
      ErpStudentResponse studentResponse,
      ErpFeeResponse feeResponse) {}

  @Builder
  public record ErpEntityChangeResponse(
      List<ErpEntityChange> erpEntityChanges, List<String> admissionNumbers) {}

  @Builder
  public record ErpFeeResponse(
      String studentCode,
      String studentName,
      String dueAmount,
      String admissionNo,
      String rollNo,
      String className,
      String schoolId,
      String orgSlug) {}

  @Builder
  public record Response(
      String orgSlug,
      int totalStudents,
      int addStudent,
      int updateStudent,
      int deleteStudent,
      int unchangedStudents,
      Integer activeStudents,
      int inActiveStudents,
      int totalActiveInActiveStudents,
      String type,
      List<StudentProcessResponse> studentResponses,
      List<SectionChangedStudents> sectionChangedStudents) {}

  @Builder
  public record StudentProcessResponse(
      String name, String section, String grade, String board, String changeType, Long dueAmount) {}

  @Builder
  public record SectionChangedStudents(
      String studentCode, String name, String wexlSection, String erpSection, String orgSlug) {}
}
