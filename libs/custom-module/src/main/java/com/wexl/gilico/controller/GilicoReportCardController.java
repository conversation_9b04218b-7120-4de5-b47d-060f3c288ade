package com.wexl.gilico.controller;

import com.wexl.gilico.service.GillcoParentIdCard;
import com.wexl.holisticreportcards.ProgressCardService;
import com.wexl.holisticreportcards.dto.ProgressCardDto;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/gillco-progress-card")
@RequiredArgsConstructor
public class GilicoReportCardController {

  private final ProgressCardService gillcoReportCardService;
  private final GillcoParentIdCard gillcoParentIdCard;

  @PostMapping()
  public ProgressCardDto.ProgressCardResponse saveProgressCard(
      @PathVariable("orgSlug") String orgSlug, @RequestBody ProgressCardDto.Request request) {
    return gillcoReportCardService.saveProgressCard(orgSlug, request);
  }

  @GetMapping("/students/{studentAuthUserId}")
  public ProgressCardDto.Response getProgressCardById(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("studentAuthUserId") String studentAuthUserId) {
    return gillcoReportCardService.getProgressCardById(orgSlug, studentAuthUserId);
  }

  @PostMapping("/students/{studentAuthUserId}/preview_report")
  public byte[] getMarksEntryReport(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("studentAuthUserId") String studentAuthUserId) {
    return gillcoReportCardService.getMarksEntryReport(orgSlug, studentAuthUserId);
  }

  @PostMapping(value = "/guardian-card")
  public void generateGuardianCard(
      @PathVariable String orgSlug,
      @RequestParam(value = "grade_slug", required = false) String gradeSlug,
      @RequestParam(value = "board_slug", required = false) String boardSlug,
      @RequestParam(value = "section_uuid", required = false) String sectionUuid) {
    gillcoParentIdCard.generateGuardianCard(orgSlug, gradeSlug, boardSlug, sectionUuid);
  }

  @GetMapping("/guardian-card")
  public String getGuardianCard(
      @PathVariable String orgSlug,
      @RequestParam(value = "student_auth_id", required = false) String studentAuthId,
      @RequestParam(value = "grade_slug", required = false) String gradeSlug,
      @RequestParam(value = "board_slug", required = false) String boardSlug,
      @RequestParam(value = "section_uuid", required = false) String sectionUuid) {
    return gillcoParentIdCard.getGuardianCard(
        orgSlug, studentAuthId, gradeSlug, boardSlug, sectionUuid);
  }
}
