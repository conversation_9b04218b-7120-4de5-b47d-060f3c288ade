package com.wexl.dps.reportcard;

import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.erp.attendance.dto.MedicalRecords;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceDetailRepository;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceRepository;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.DoonBhartiReportCardDto;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class Doon4th8thTerm1ReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final StudentAttributeService studentAttributeService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final SectionAttendanceRepository sectionAttendanceRepository;
  private final LowerGradeFirstTermReportCard lowerGradeFirstTermReportCard;
  private final SectionAttendanceDetailRepository sectionAttendanceDetailRepository;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeaderData(user, org);
    var body = buildBody(user.getStudentInfo(), org);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("doon-bharti-4th-8th-report-card.xml");
  }

  private DoonBhartiReportCardDto.Header buildHeaderData(User user, Organization org) {
    Student student = user.getStudentInfo();
    var guardians = student.getGuardians();
    var father =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.FATHER)).findAny();
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    Optional<StudentAttributeValueModel> address =
        reportCardService.getStudentAttributeValue(student, "residental_address");
    return DoonBhartiReportCardDto.Header.builder()
        .schoolName(org.getName())
        .sessionStart("25")
        .sessionEnd("26")
        .orgSlug(org.getSlug())
        .className(student.getSection().getGradeName())
        .sectionName(student.getSection().getName())
        .studentName(user.getFirstName() + " " + user.getLastName())
        .fatherName(
            father
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .rollNo(getRollNumber(student.getClassRollNumber()))
        .gradeSlug(student.getSection().getGradeSlug())
        .dob(studentAttributeService.getDateOfBirthFormat(dateOfBirth))
        .admissionNumber(student.getRollNumber())
        .address(address.map(StudentAttributeValueModel::getValue).orElse(null))
        .phoneNumber(user.getMobileNumber())
        .build();
  }

  public DoonBhartiReportCardDto.Body buildBody(Student student, Organization org) {
    var tableMarks = buildTableMarks(student);

    var section = student.getSection();
    var attendance =
        sectionAttendanceRepository.getStudentsAttendanceReport(
            org.getSlug(),
            List.of(section.getGradeSlug()),
            List.of(section.getId()),
            List.of(1L),
            20250401,
            20250923);
    var studentsAttendance =
        attendance.stream()
            .filter(a -> student.getUserInfo().getAuthUserId().equals(a.getAuthId()))
            .findAny()
            .orElse(null);
    var attendanceDetails = lowerGradeFirstTermReportCard.buildAttendance(student.getId());
    var studentData =
        sectionAttendanceDetailRepository.getMedicalRecords(student.getId(), org.getSlug());

    return DoonBhartiReportCardDto.Body.builder()
        .firstTable(buildFirstTable(tableMarks.firstTableMarks()))
        .secondTable(buildSecondTable(tableMarks.secondTableMarks()))
        .totalWorkingDays(studentsAttendance != null ? studentsAttendance.getTotalWorkingDays() : 0)
        .totalPresentDays(studentsAttendance != null ? studentsAttendance.getPresentDays() : 0)
        .term("t1")
        .isParticipated(attendanceDetails.olympiad())
        .isParticipatedInInternal(attendanceDetails.internalOlympiad())
        .olympiadSubjects(attendanceDetails.olympiadSubjects())
        .internalOlympiadSubjects(attendanceDetails.internalOlympiadSubjects())
        .height(studentData.map(MedicalRecords::getHeight).orElse(null))
        .weight(studentData.map(MedicalRecords::getWeight).orElse(null))
        .build();
  }

  public DoonBhartiReportCardDto.FirstTable buildFirstTable(
      List<DoonBhartiReportCardDto.Marks> marks) {
    return DoonBhartiReportCardDto.FirstTable.builder().marks(marks).build();
  }

  public DoonBhartiReportCardDto.SecondTable buildSecondTable(
      List<DoonBhartiReportCardDto.SecMarks> marks) {
    return DoonBhartiReportCardDto.SecondTable.builder().marks(marks).build();
  }

  private DoonBhartiReportCardDto.TableMarks buildTableMarks(Student student) {

    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            Collections.singletonList("t1"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var coScholasticData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .toList();

    var sortedData =
        sortTable(
            buildFirstTableMarks(scholasticDataList), buildSecondTableMarks(coScholasticData));
    return DoonBhartiReportCardDto.TableMarks.builder()
        .firstTableMarks(sortedData.firstTableMarks())
        .secondTableMarks(sortedData.secondTableMarks())
        .build();
  }

  public List<DoonBhartiReportCardDto.Marks> buildFirstTableMarks(
      List<LowerGradeReportCardData> reportCardData) {
    List<DoonBhartiReportCardDto.Marks> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var ut1 = getMarks(List.of("ut1"), scholasticData);
          var portfolio = getMarks(List.of("portfolio"), scholasticData);
          var ma = getMarks(List.of("se1"), scholasticData);
          var hye = getMarks(List.of("hye"), scholasticData);
          var cd = getMarks(List.of("cd"), scholasticData);

          var term1Total = sumMarks(ut1, portfolio, ma, hye, cd);
          var termMaxMarks = 100d;
          var offlineTestDefinition =
              offlineTestScheduleService.validateOfflineTestDefinition(
                  scholasticData.getFirst().getOtdId());
          String grade =
              calculateGrade(term1Total, termMaxMarks, offlineTestDefinition.getGradeScaleSlug());
          double term1TotalDouble = Double.parseDouble(String.format("%.1f", term1Total));

          marksList.add(
              DoonBhartiReportCardDto.Marks.builder()
                  .subjectName(subject)
                  .unitTest1(buildMarks(scholasticData, ut1))
                  .portFolio(buildMarks(scholasticData, portfolio))
                  .cognitiveDevelopment(buildMarks(scholasticData, cd))
                  .ma(buildMarks(scholasticData, ma))
                  .halfYearly(buildMarks(scholasticData, hye))
                  .total(term1TotalDouble)
                  .grade(grade)
                  .build());
        });
    return marksList;
  }

  public List<DoonBhartiReportCardDto.SecMarks> buildSecondTableMarks(
      List<LowerGradeReportCardData> reportCardData) {
    List<DoonBhartiReportCardDto.SecMarks> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var pt = getMarks(List.of("pt(pt1+pt2)", "pa1+cra1"), scholasticData);
          var nb1 = getMarks(List.of("nb1"), scholasticData);
          var se1 = getMarks(List.of("se1"), scholasticData);
          var hye = getMarks(List.of("hye"), scholasticData);

          var term1Total = sumMarks(pt, nb1, se1, hye);
          var termMaxMarks = 100d;
          var offlineTestDefinition =
              offlineTestScheduleService.validateOfflineTestDefinition(
                  scholasticData.getFirst().getOtdId());
          String grade =
              calculateGrade(term1Total, termMaxMarks, offlineTestDefinition.getGradeScaleSlug());

          marksList.add(
              DoonBhartiReportCardDto.SecMarks.builder().subjectName(subject).grade(grade).build());
        });
    return marksList;
  }

  public String buildMarks(List<LowerGradeReportCardData> scholasticData, String value) {
    if (value == null) {
      return null;
    }
    return value.equals("0.0")
        ? (Boolean.TRUE.equals(Boolean.valueOf(scholasticData.getFirst().getIsAttended()))
            ? null
            : "AB")
        : value;
  }

  public String getMarks(List<String> assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    Double average;
    var data =
        subjectData.stream().filter(d -> assessmentSlug.contains(d.getAssessmentSlug())).toList();
    if (data.isEmpty()) {
      return null;
    }

    var isAttended = data.stream().filter(d -> d.getIsAttended() == null).toList();
    if (!isAttended.isEmpty()) {
      return null;
    }

    var data1 =
        data.stream().filter(d -> Boolean.TRUE.equals(Boolean.valueOf(d.getIsAttended()))).toList();

    if (data1.isEmpty()) {
      return data.getFirst().getRemarks() == null
          ? "AB"
          : data.getFirst().getRemarks().substring(0, 2).toUpperCase();
    }

    average =
        data1.stream()
            .map(LowerGradeReportCardData::getMarks)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .average()
            .orElse(0.0);

    return String.format("%.2f", average);
  }

  public Double sumMarks(String... marks) {
    return Arrays.stream(marks)
        .filter(mark -> mark != null && !mark.isEmpty() && mark.matches("\\d+(\\.\\d+)?"))
        .mapToDouble(Double::parseDouble)
        .sum();
  }

  public String calculateGrade(Double marks, Double totalMarks, String gradeScaleSlug) {
    return marks == null || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate(
            gradeScaleSlug == null ? "9point" : gradeScaleSlug,
            BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private DoonBhartiReportCardDto.TableMarks sortTable(
      List<DoonBhartiReportCardDto.Marks> firstTableMarks,
      List<DoonBhartiReportCardDto.SecMarks> secondTableMarks) {
    firstTableMarks.sort(Comparator.comparing(DoonBhartiReportCardDto.Marks::subjectName));
    secondTableMarks.sort(Comparator.comparing(DoonBhartiReportCardDto.SecMarks::subjectName));
    return DoonBhartiReportCardDto.TableMarks.builder()
        .firstTableMarks(firstTableMarks)
        .secondTableMarks(secondTableMarks)
        .build();
  }
}
