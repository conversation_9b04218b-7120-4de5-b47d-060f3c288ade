package com.wexl.dps.reportcard;

import com.wexl.dps.dto.NinthTenthGradeReportCardDto;
import com.wexl.dps.dto.SportsReportCardDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.model.SubjectsMetadataStudents;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.subjects.repository.SubjectsMetadataStudentsRepository;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UpperGradeReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final StudentAttributeService studentAttributeService;
  private static final String FALSE = "false";
  private final SportsReportCard sportsReportCard;
  private final LowerGradeOverallReportCard lowerGradeOverallReportCard;
  private final SubjectsMetadataStudentsRepository subjectsMetadataStudentsRepository;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("9th-10th-grade-report-card.xml");
  }

  public NinthTenthGradeReportCardDto.Body buildBody(User user, String orgSlug) {
    var student = user.getStudentInfo();

    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var tableMarks = buildTableMarks(student);
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(
            tableMarks.firstTableMarks().getFirst().otdId());

    List<Long> termIds = List.of(1L, 2L);
    List<SportsReportCardDto.Overall> physData = new ArrayList<>();
    for (Long termId : termIds) {
      List<SportsReportCardDto.MainTable> physicalData =
          lowerGradeOverallReportCard.buildPhysicalData(user, termId);
      physData.add(sportsReportCard.getLearningLevelByListOfGrades(physicalData));
    }
    return NinthTenthGradeReportCardDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(studentAttributeService.getDateOfBirthFormat(dateOfBirth))
        .className(student.getSection().getName())
        .rollNumber(student.getClassRollNumber())
        .orgSlug(orgSlug)
        .boardSlug(student.getSection().getBoardSlug())
        .gradeSlug(student.getSection().getGradeSlug())
        .mothersName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .firstTable(buildFirstTable(tableMarks.firstTableMarks(), tableMarks.externalMarks()))
        .secondTable(buildSecondTable(tableMarks.secondTableMarks()))
        .thirdTable(buildThirdTable(tableMarks.thirdTableMarks()))
        .fourthTable(buildFourthTable(physData, student))
        // .graphResponse(buildGraphResponse(tableMarks.graphResponse()))
        .gradeTable(NinthTenthGradeReportCardDto.GradeTable.builder().title("Grade Scale").build())
        .attendance(buildAttendance(student.getId()))
        .gradingScale(
            testDefinition.getGradeScaleSlug() != null
                ? testDefinition.getGradeScaleSlug()
                : "8point")
        .build();
  }

  /*private NinthTenthGradeReportCardDto.FirstTable buildGraphResponse(
          List<NinthTenthGradeReportCardDto.Marks> marks) {
    NinthTenthGradeReportCardDto.FirstTable graphResponse =
            NinthTenthGradeReportCardDto.FirstTable.builder().marks(marks).build();
    return graphResponse;
  }*/

  private List<NinthTenthGradeReportCardDto.FourthTable> buildFourthTable(
      List<SportsReportCardDto.Overall> physData, Student student) {
    List<SubjectsMetadataStudents> subjectsMetadataStudentsList =
        subjectsMetadataStudentsRepository.findByStudentId(student.getId());
    List<SubjectsMetaData> subjectsMetaData =
        subjectsMetadataStudentsList.stream()
            .filter(
                stu ->
                    stu.getSubjectsMetaData() != null
                        && "sports".equals(stu.getSubjectsMetaData().getWexlSubjectSlug()))
            .map(stu -> stu.getSubjectsMetaData())
            .collect(Collectors.toList());
    if (subjectsMetaData.isEmpty()) {
      return Collections.emptyList();
    }
    SubjectsMetaData subjectsMetaDataFirst = subjectsMetaData.getFirst();

    return physData.stream()
        .map(
            data ->
                NinthTenthGradeReportCardDto.FourthTable.builder()
                    .subjectName(subjectsMetaDataFirst.getName())
                    .term1Grade(data.overallGrade())
                    .build())
        .toList();
  }

  private NinthTenthGradeReportCardDto.TableMarks buildTableMarks(Student student) {

    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            List.of("t1", "t2"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var optionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();

    var coScholasticData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var coScholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType())
                        && (x.getAssessmentSlug().equals("ae-coscholastic")
                            || x.getAssessmentSlug().equals("ae")))
            .toList();

    var sortedData =
        sortTable(
            buildTableMarks(scholasticDataList, student.getSection().getGradeSlug()),
            buildScholasticOptionalData(optionalData, student.getSection().getGradeSlug()),
            // buildTableMarks(optionalData, student.getSection().getGradeSlug()),
            buildCoScholasticTableMarks(coScholasticData, student.getSection().getGradeSlug()),
            buildCoScholasticTableMarks(
                coScholasticOptionalData, student.getSection().getGradeSlug()));
    return NinthTenthGradeReportCardDto.TableMarks.builder()
        .firstTableMarks(sortedData.firstTableMarks())
        .externalMarks(sortedData.externalMarks())
        .secondTableMarks(sortedData.secondTableMarks())
        .thirdTableMarks(sortedData.thirdTableMarks())
        // .graphResponse(sortedData.graphResponse())
        .build();
  }

  private List<NinthTenthGradeReportCardDto.Marks> buildScholasticOptionalData(
      List<LowerGradeReportCardData> scholasticDataList, String gradeSlug) {
    List<NinthTenthGradeReportCardDto.Marks> marksList = new ArrayList<>();
    var scholasticDataMap =
        scholasticDataList.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var ae = getOptionalMarks(List.of("ae"), scholasticData);
          double totalMarks =
              scholasticData.stream()
                  .filter(data -> "ae".equals(data.getAssessmentSlug()))
                  .mapToDouble(LowerGradeReportCardData::getSubjectMarks)
                  .sum();

          var offlineTestDefinition =
              offlineTestScheduleService.validateOfflineTestDefinition(
                  scholasticData.getFirst().getOtdId());
          String grade =
              calculateGrade(
                  (ae == null ? null : Double.valueOf(ae)),
                  totalMarks,
                  offlineTestDefinition.getGradeScaleSlug());

          marksList.add(
              NinthTenthGradeReportCardDto.Marks.builder()
                  .totalMarksScored(ae)
                  .grade(grade)
                  .seqNo(scholasticData.getFirst().getSeqNo())
                  .otdId(scholasticData.getFirst().getOtdId())
                  .subject(subject)
                  .build());
        });
    return marksList;
  }

  public List<NinthTenthGradeReportCardDto.Marks> buildTableMarks(
      List<LowerGradeReportCardData> reportCardData, String gradeSlug) {
    List<NinthTenthGradeReportCardDto.Marks> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var port = getMarks(List.of("portfolio"), scholasticData);
          var se = getMarks(List.of("se"), scholasticData);
          var ae = getMarks(List.of("ae"), scholasticData);
          var pt = getPtMarks(List.of("pa1", "pa2", "pa3"), scholasticData);
          var pt1 = getPtMarks(List.of("pa1"), scholasticData);
          var pt2 = getPtMarks(List.of("pa2"), scholasticData);
          var pt3 = getPtMarks(List.of("pa3"), scholasticData);
          var ma = getMarks(List.of("ma"), scholasticData);

          var internalAssessmentMarks = sumMarks(pt, se, ma, port);
          var overAllMarks = 100d;
          double totalMarks = internalAssessmentMarks + 0.0;

          if ("x".equals(gradeSlug) && (ae == null || ae.trim().isEmpty() || "0".equals(ae))) {
            ae = "";
            overAllMarks = 20d;
          } else {
            totalMarks =
                Double.parseDouble(
                    String.format(
                        "%.2f",
                        internalAssessmentMarks
                            + (NumberUtils.isCreatable(ae) ? Double.parseDouble(ae) : 0.0)));
          }

          var offlineTestDefinition =
              offlineTestScheduleService.validateOfflineTestDefinition(
                  scholasticData.getFirst().getOtdId());
          String grade =
              calculateGrade(totalMarks, overAllMarks, offlineTestDefinition.getGradeScaleSlug());

          marksList.add(
              NinthTenthGradeReportCardDto.Marks.builder()
                  .internalAssessmentMarks(
                      Double.valueOf(String.format("%.2f", internalAssessmentMarks)))
                  .annualMarks("".equals(ae) ? null : ae)
                  .subject(subject)
                  .grade(grade)
                  .pt1(calculatePercentageAsString(pt1, 5))
                  .pt2(calculatePercentageAsString(pt2, 5))
                  .pt3(calculatePercentageAsString(pt3, 5))
                  .annualMarksInPercentage(calculatePercentageAsString(ae, 80))
                  .totalMarksScored(String.format("%.2f", totalMarks))
                  .total(String.valueOf(overAllMarks))
                  .seqNo(scholasticData.getFirst().getSeqNo())
                  .otdId(scholasticData.getFirst().getOtdId())
                  .build());
        });
    return marksList;
  }

  public static int calculatePercentageAsString(String marks, double maxMarks) {
    if (marks == null || marks.isEmpty()) {
      return 0;
    }
    try {
      double marksObtained = Double.parseDouble(marks);
      return (int) Math.round((marksObtained / maxMarks) * 100);
    } catch (NumberFormatException e) {
      return 0;
    }
  }

  public List<NinthTenthGradeReportCardDto.Marks> buildCoScholasticTableMarks(
      List<LowerGradeReportCardData> reportCardData, String gradeSlug) {
    List<NinthTenthGradeReportCardDto.Marks> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .collect(Collectors.groupingBy(LowerGradeReportCardData::getSubjectName));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var port = getMarks(List.of("portfolio"), scholasticData);
          var se = getMarks(List.of("se"), scholasticData);
          String assessmentSlug =
              gradeSlug.equals("ix")
                      && scholasticData.stream()
                          .map(LowerGradeReportCardData::getAssessmentSlug)
                          .collect(Collectors.toSet())
                          .contains("ae-coscholastic")
                  ? "ae-coscholastic"
                  : "ae";
          var ae = getMarks(List.of(assessmentSlug), scholasticData);
          var pt = getPtMarks(List.of("pa1", "pa2", "pa3"), scholasticData);
          var ma = getMarks(List.of("ma"), scholasticData);

          var internalAssessmentMarks = sumMarks(pt, se, ma, port);
          var overAllMarks = 100d;
          double totalMarks =
              Double.parseDouble(
                  String.format(
                      "%.2f",
                      internalAssessmentMarks
                          + (NumberUtils.isCreatable(ae) ? Double.parseDouble(ae) : 0.0)));
          var offlineTestDefinition =
              offlineTestScheduleService.validateOfflineTestDefinition(
                  scholasticData.getFirst().getOtdId());
          String grade;
          if (scholasticData
              .getFirst()
              .getIsAttended()
              .equalsIgnoreCase(Boolean.FALSE.toString().toLowerCase())) {
            grade =
                scholasticData.getFirst().getRemarks() != null
                    ? scholasticData.getFirst().getRemarks()
                    : "AB";
          } else {
            grade =
                calculateGrade(totalMarks, overAllMarks, offlineTestDefinition.getGradeScaleSlug());
          }

          marksList.add(
              NinthTenthGradeReportCardDto.Marks.builder()
                  .internalAssessmentMarks(internalAssessmentMarks)
                  .annualMarks(ae)
                  .subject(subject)
                  .isAttended(scholasticData.getFirst().getIsAttended())
                  .grade(grade)
                  .totalMarksScored(String.valueOf(totalMarks))
                  .total(String.valueOf(overAllMarks))
                  .seqNo(scholasticData.getFirst().getSeqNo())
                  .otdId(scholasticData.getFirst().getOtdId())
                  .build());
        });
    return marksList;
  }

  private String getPtMarks(
      List<String> assessmentSlugs, List<LowerGradeReportCardData> subjectData) {

    List<LowerGradeReportCardData> filteredData =
        subjectData.stream()
            .filter(d -> assessmentSlugs.contains(d.getAssessmentSlug()))
            .filter(d -> d.getMarks() != null && d.getSubjectMarks() != null)
            .toList();

    if (filteredData.isEmpty()) {
      return null;
    }

    List<LowerGradeReportCardData> sortedData =
        filteredData.stream()
            .sorted(
                (d1, d2) -> {
                  double percentage1 = (d1.getMarks() / d1.getSubjectMarks()) * 100;
                  double percentage2 = (d2.getMarks() / d2.getSubjectMarks()) * 100;
                  return Double.compare(percentage2, percentage1);
                })
            .limit(2)
            .toList();

    double totalMarks = sortedData.stream().mapToDouble(LowerGradeReportCardData::getMarks).sum();
    double totalSubjectMarks =
        sortedData.stream().mapToDouble(LowerGradeReportCardData::getSubjectMarks).sum();

    double average = (totalMarks / totalSubjectMarks) * 5;

    return String.format("%.2f", average);
  }

  private double parseMark(String mark) {
    if (mark == null || mark.isEmpty()) {
      return 0.0;
    }
    try {
      return Double.parseDouble(mark);
    } catch (NumberFormatException e) {
      return 0.0;
    }
  }

  private String calculateGrade(Double marks, Double totalMarks, String gradeScaleSlug) {
    return marks == null || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate(
            gradeScaleSlug == null ? "8point" : gradeScaleSlug,
            BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  public String getMarks(List<String> assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    List<LowerGradeReportCardData> data;
    Double average;

    data =
        subjectData.stream().filter(d -> assessmentSlug.contains(d.getAssessmentSlug())).toList();

    if (data.isEmpty()) {
      return null;
    }

    var isAttended = data.stream().filter(d -> d.getIsAttended() == null).toList();
    if (!isAttended.isEmpty()) {
      return null;
    }

    var data1 =
        data.stream().filter(d -> Boolean.TRUE.equals(Boolean.valueOf(d.getIsAttended()))).toList();

    if (data1.isEmpty()) {
      return data.getFirst().getRemarks() == null
          ? "AB"
          : data.getFirst().getRemarks().substring(0, 2).toUpperCase();
    }

    average =
        data1.stream()
            .filter(datas -> datas.getMarks() != null)
            .mapToDouble(datas -> datas.getMarks())
            .average()
            .orElse(0.0);

    return String.format("%.2f", average);
  }

  public Double sumMarks(String... marks) {
    return Arrays.stream(marks)
        .filter(mark -> mark != null && !mark.isEmpty() && mark.matches("\\d+(\\.\\d+)?"))
        .mapToDouble(Double::parseDouble)
        .sum();
  }

  private NinthTenthGradeReportCardDto.FirstTable buildFirstTable(
      List<NinthTenthGradeReportCardDto.Marks> firstTableMarks,
      List<NinthTenthGradeReportCardDto.Marks> externalMarks) {

    NinthTenthGradeReportCardDto.FirstTable firstTable =
        NinthTenthGradeReportCardDto.FirstTable.builder()
            .title("PART-I: SCHOLASTIC AREAS [ON a 8-POINT (A1 to E) GRADING SCALE]")
            .marks(firstTableMarks)
            .external(externalMarks)
            .totals(buildTotals(firstTableMarks))
            .build();
    return firstTable;
  }

  private NinthTenthGradeReportCardDto.Totals buildTotals(
      List<NinthTenthGradeReportCardDto.Marks> firstTableMarks) {
    double totalMarksScored =
        firstTableMarks.stream()
            .map(NinthTenthGradeReportCardDto.Marks::totalMarksScored)
            .filter(mark -> mark != null && mark.matches("\\d+(\\.\\d+)?"))
            .mapToDouble(Double::parseDouble)
            .sum();

    double totalMarks =
        firstTableMarks.stream()
            .map(NinthTenthGradeReportCardDto.Marks::annualMarks)
            .filter(mark -> mark != null && mark.matches("\\d+(\\.\\d+)?"))
            .mapToDouble(Double::parseDouble)
            .sum();

    double totalMarks1 =
        firstTableMarks.stream()
            .map(NinthTenthGradeReportCardDto.Marks::total)
            .filter(mark -> mark != null && mark.matches("\\d+(\\.\\d+)?"))
            .mapToDouble(Double::parseDouble)
            .sum();

    double percentage = (totalMarksScored / totalMarks1) * 100;

    percentage = Double.parseDouble(String.format("%.2f", percentage));

    String totalMarksScoredFormat = String.format("%.2f", totalMarksScored);

    String grade = pointScaleEvaluator.evaluate("8point", BigDecimal.valueOf(percentage));

    String overallPercentageGrade = grade;
    return NinthTenthGradeReportCardDto.Totals.builder()
        .marksTotal(totalMarks)
        .percentage(Double.valueOf(totalMarksScoredFormat))
        .overallPercentage(percentage)
        .grade(overallPercentageGrade)
        .build();
  }

  private NinthTenthGradeReportCardDto.SecondTable buildSecondTable(
      List<NinthTenthGradeReportCardDto.SecondTableMarks> marks) {
    return NinthTenthGradeReportCardDto.SecondTable.builder()
        .title("PART-II: Co-Scholastic Areas[on a 5-point (A to E) grading scale]")
        .marks(marks)
        .build();
  }

  private NinthTenthGradeReportCardDto.ThirdTable buildThirdTable(
      List<NinthTenthGradeReportCardDto.ThirdTableMarks> marks) {
    return NinthTenthGradeReportCardDto.ThirdTable.builder()
        .title("PART-II: Co-Scholastic Areas[on a 5-point (A to E) grading scale]")
        .marks(marks)
        .build();
  }

  public NinthTenthGradeReportCardDto.Attendance buildAttendance(Long studentId) {
    var termAssessment = termAssessmentRepository.findBySlug("ae");
    if (termAssessment.isEmpty()) {
      return NinthTenthGradeReportCardDto.Attendance.builder().build();
    }
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository
            .getOfflineTestStudentAttendanceByStudentAndAssessmentId(
                studentId, termAssessment.get().getId());
    if (studentAttendance.isEmpty()) {
      return NinthTenthGradeReportCardDto.Attendance.builder().build();
    }

    var testDefinition = studentAttendance.get().getOfflineTestDefinition();
    if (testDefinition == null) {
      throw new IllegalArgumentException("Test definition not found for studentId: " + studentId);
    }
    String totalAttendanceDays = testDefinition.getTotalAttendanceDays();
    Long daysPresent = studentAttendance.get().getPresentDays();
    if (totalAttendanceDays == null || daysPresent == null) {
      return NinthTenthGradeReportCardDto.Attendance.builder().build();
    }

    Double attendancePercentage = null;
    double totalDays = Double.parseDouble(totalAttendanceDays);
    if (totalDays > 0.0) {
      attendancePercentage = (double) Math.round((daysPresent.doubleValue() / totalDays) * 100);
    }
    return NinthTenthGradeReportCardDto.Attendance.builder()
        .workingDays(Long.valueOf(totalAttendanceDays))
        .daysPresent(daysPresent)
        .attendancePercentage(attendancePercentage)
        .remarks(studentAttendance.get().getRemarks())
        .build();
  }

  private NinthTenthGradeReportCardDto.TableMarks sortTable(
      List<NinthTenthGradeReportCardDto.Marks> firstTableMarks,
      List<NinthTenthGradeReportCardDto.Marks> externalMarks,
      List<NinthTenthGradeReportCardDto.Marks> secondTableMarks,
      List<NinthTenthGradeReportCardDto.Marks> thirdTableMarks) {
    List<NinthTenthGradeReportCardDto.Marks> firstTable = new ArrayList<>();
    List<NinthTenthGradeReportCardDto.SecondTableMarks> secondTable = new ArrayList<>();
    List<NinthTenthGradeReportCardDto.ThirdTableMarks> thirdTable = new ArrayList<>();
    List<NinthTenthGradeReportCardDto.Marks> externalTable = new ArrayList<>();
    // List<NinthTenthGradeReportCardDto.Marks> graphResponse = new ArrayList<>();
    var sortedFirstTable =
        firstTableMarks.stream()
            .sorted(Comparator.comparingLong(NinthTenthGradeReportCardDto.Marks::seqNo))
            .toList();

    for (int i = 0; i < sortedFirstTable.size(); i++) {
      NinthTenthGradeReportCardDto.Marks mark = sortedFirstTable.get(i);
      firstTable.add(
          NinthTenthGradeReportCardDto.Marks.builder()
              .sno(i + 1L)
              .grade(mark.grade())
              .annualMarks(mark.annualMarks())
              .totalMarksScored(mark.totalMarksScored())
              .pt1(mark.pt1())
              .pt2(mark.pt2())
              .pt3(mark.pt3())
              .annualMarksInPercentage(mark.annualMarksInPercentage())
              .total(mark.total())
              .internalAssessmentMarks(mark.internalAssessmentMarks())
              .subject(mark.subject())
              .otdId(mark.otdId())
              .build());
    }

    var sortedExternalTable =
        externalMarks.stream()
            .sorted(Comparator.comparingLong(NinthTenthGradeReportCardDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedExternalTable.size(); i++) {
      NinthTenthGradeReportCardDto.Marks mark = sortedExternalTable.get(i);
      externalTable.add(
          NinthTenthGradeReportCardDto.Marks.builder()
              .sno(sortedFirstTable.size() + i + 1L)
              .grade(mark.grade())
              .annualMarks(mark.annualMarks())
              .totalMarksScored(mark.totalMarksScored())
              .total(mark.total())
              .internalAssessmentMarks(mark.internalAssessmentMarks())
              .subject(mark.subject())
              .otdId(mark.otdId())
              .build());
    }
    var sortedSecondTable =
        secondTableMarks.stream()
            .sorted(Comparator.comparingLong(NinthTenthGradeReportCardDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedSecondTable.size(); i++) {
      NinthTenthGradeReportCardDto.Marks mark = sortedSecondTable.get(i);
      var hye = mark.totalMarksScored();
      String grade = null;

      if (hye != null && !hye.equals("AB") && !hye.equals("PA") && !hye.equals("ML")) {
        grade = pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(Double.parseDouble(hye)));
      }

      secondTable.add(
          NinthTenthGradeReportCardDto.SecondTableMarks.builder()
              .sno(i + 1L)
              .termGrade(Objects.isNull(grade) ? hye : grade)
              .subjectName(mark.subject())
              .build());
    }

    var sortedThirdTable =
        thirdTableMarks.stream()
            .sorted(Comparator.comparingLong(NinthTenthGradeReportCardDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedThirdTable.size(); i++) {
      NinthTenthGradeReportCardDto.Marks mark = sortedThirdTable.get(i);
      var hye = mark.totalMarksScored();
      String grade = null;

      if (hye != null && !hye.equals("AB") && !hye.equals("PA") && !hye.equals("ML")) {
        grade = pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(Double.parseDouble(hye)));
      }
      if (mark.isAttended().equals("false")) {
        grade = mark.grade();
      }
      thirdTable.add(
          NinthTenthGradeReportCardDto.ThirdTableMarks.builder()
              .sno(i + 1L)
              .termGrade(Objects.isNull(grade) ? hye : grade)
              .subjectName(mark.subject())
              .build());
    }

    // graphResponse.addAll(firstTableMarks);
    // graphResponse.addAll(externalMarks);

    return NinthTenthGradeReportCardDto.TableMarks.builder()
        .firstTableMarks(firstTable)
        .externalMarks(externalTable)
        .secondTableMarks(secondTable)
        .thirdTableMarks(thirdTable)
        // .graphResponse(graphResponse)
        .build();
  }

  private String getOptionalMarks(
      List<String> assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    List<LowerGradeReportCardData> data;
    Double average;

    data =
        subjectData.stream().filter(d -> assessmentSlug.contains(d.getAssessmentSlug())).toList();

    if (data.isEmpty()) {
      return null;
    }

    var isAttended = data.stream().filter(d -> d.getIsAttended() == null).toList();
    if (!isAttended.isEmpty()) {
      return null;
    }

    var data1 =
        data.stream().filter(d -> Boolean.TRUE.equals(Boolean.valueOf(d.getIsAttended()))).toList();

    if (data1.isEmpty()) {
      return data.getFirst().getRemarks() == null
          ? "AB"
          : data.getFirst().getRemarks().substring(0, 2).toUpperCase();
    }

    average =
        data1.stream()
            .filter(datas -> datas.getActualMarks() != null)
            .mapToDouble(datas -> datas.getActualMarks())
            .average()
            .orElse(0.0);

    return String.format("%.2f", average);
  }

  public List<ReportCardConfigDto.GradeAndPercentageResponse> getAGradeResponse(
      List<Student> students) {

    List<ReportCardConfigDto.GradeAndPercentageResponse> responseList = new ArrayList<>();
    students.forEach(
        student -> {
          try {
            var tableMarks = buildTableMarks(student);
            var firstTable =
                buildFirstTable(tableMarks.firstTableMarks(), tableMarks.externalMarks());

            if (firstTable.totals().grade().equals("A1")) {

              var userInfo = student.getUserInfo();
              var name = userInfo.getFirstName() + " " + userInfo.getLastName();
              responseList.add(
                  ReportCardConfigDto.GradeAndPercentageResponse.builder()
                      .grade(firstTable.totals().grade())
                      .studentName(name)
                      .studentId(student.getId())
                      .sectionName(student.getSection().getName())
                      .build());
            }

          } catch (Exception ex) {
          }
        });
    return responseList;
  }

  public List<ReportCardConfigDto.GradeAndPercentageResponse> getAGradeAllSubjectsSummary(
      List<Student> students) {

    List<ReportCardConfigDto.GradeAndPercentageResponse> responseList = new ArrayList<>();
    students.forEach(
        student -> {
          try {
            var tableMarks = buildTableMarks(student);
            var firstTable =
                buildFirstTable(tableMarks.firstTableMarks(), tableMarks.externalMarks());
            boolean allSubjectsA1 =
                firstTable.marks().stream().allMatch(subject -> "A1".equals(subject.grade()));

            if (allSubjectsA1) {
              var userInfo = student.getUserInfo();
              var name = userInfo.getFirstName() + " " + userInfo.getLastName();

              responseList.add(
                  ReportCardConfigDto.GradeAndPercentageResponse.builder()
                      .grade(firstTable.totals().grade())
                      .studentName(name)
                      .studentId(student.getId())
                      .sectionName(student.getSection().getName())
                      .build());
            }

          } catch (Exception ex) {
          }
        });
    return responseList;
  }

  public List<ReportCardConfigDto.GradeAndPercentage> getGradeAndPercentage(
      List<Student> students) {
    List<ReportCardConfigDto.GradeAndPercentage> responseList = new ArrayList<>();
    students.forEach(
        student -> {
          try {
            var tableMarks = buildTableMarks(student);
            var firstTable = tableMarks.firstTableMarks();
            var totals = buildTotals(firstTable);
            responseList.add(
                ReportCardConfigDto.GradeAndPercentage.builder()
                    .grade(String.valueOf(totals.grade()))
                    .percentage(
                        totals.marksTotal().toString().equals("NaN")
                            ? "-"
                            : totals.overallPercentage().toString())
                    .student(student)
                    .build());
          } catch (Exception ex) {
            responseList.add(
                ReportCardConfigDto.GradeAndPercentage.builder()
                    .grade("NA")
                    .percentage("-")
                    .student(student)
                    .build());
          }
        });
    return responseList;
  }
}
