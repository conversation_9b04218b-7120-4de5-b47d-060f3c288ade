package com.wexl.dps.reportcard;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ParticipationCertificate extends BaseReportCardDefinition {

  private final StudentAuthService studentAuthService;
  private final ScheduleTestRepository scheduleTestRepository;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final CambridgeAdmitCard cambridgeAdmitCard;
  private final ExamRepository examRepository;

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var student = studentAuthService.validateStudentByUser(user);
    var header = buildHeaderData(student, org);
    var body = buildBody(user, request.offlineTestDefinitionId(), org);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String name = reportCardTemplate.getName();
    return name.equals("ParticipationCertificate");
  }

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return Optional.empty();
  }

  private ReportCardDto.Header buildHeaderData(Student student, Organization org) {

    return ReportCardDto.Header.builder().schoolName(org.getName()).build();
  }

  private ReportCardDto.Body buildBody(User user, Long testScheduleId, Organization organization) {
    var student = studentAuthService.validateStudentByUser(user);
    var scheduleTest =
        scheduleTestRepository
            .findById(testScheduleId)
            .orElseThrow(() -> new ApiException(InternalErrorCodes.INVALID_REQUEST, ""));
    var scheduleTestStudent =
        scheduleTestStudentRepository
            .findByScheduleTestAndStudent(scheduleTest, student.getUserInfo())
            .orElseThrow();
    if (!TestStudentStatus.COMPLETED.name().equals(scheduleTestStudent.getStatus())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.downloadReportCard");
    }

    Exam exam =
        examRepository
            .getExamDetails(student.getId(), scheduleTest.getId())
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ExamNotFound"));

    Float marksScored = exam.getMarksScored();
    Float totalMarks = exam.getTotalMarks();

    float percentage = marksScored / totalMarks * 100;
    if (percentage < 80.0) {
      String errorMessage =
          String.format(
              "Student %s scored %.1f%%, which is less than the required marks.",
              student.getUserInfo().getFirstName(), percentage);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, errorMessage);
    }

    return ReportCardDto.Body.builder()
        .name(user.getFirstName() + " " + user.getLastName())
        .orgSlug(organization.getSlug())
        .className("ParticipationCertificate")
        .rollNumber(scheduleTestStudent.getScheduleTest().getTestDefinition().getTestName())
        .startDate(cambridgeAdmitCard.dateFormat(scheduleTest.getStartDate()))
        .gradeSlug("OF GRADE " + convertGradeToNumber(student.getSection().getGradeSlug()))
        .build();
  }

  public String convertGradeToNumber(String gradeSlug) {
    switch (gradeSlug.toLowerCase()) {
      case "i":
        return "1";
      case "ii":
        return "2";
      case "iii":
        return "3";
      case "iv":
        return "4";
      case "v":
        return "5";
      case "vi":
        return "6";
      case "vii":
        return "7";
      case "viii":
        return "8";
      case "ix":
        return "9";
      case "x":
        return "10";
      case "xi":
        return "11";
      case "xig":
        return "11";
      case "xii":
        return "12";
      case "xiig":
        return "12";
      case "nur":
        return "nur";
      case "lkg":
        return "lkg";
      case "ukg":
        return "ukg";
      default:
        return null;
    }
  }
}
