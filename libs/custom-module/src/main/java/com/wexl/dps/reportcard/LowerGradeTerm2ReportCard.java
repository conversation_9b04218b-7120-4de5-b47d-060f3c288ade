package com.wexl.dps.reportcard;

import static java.lang.String.format;

import com.wexl.dps.dto.SportsReportCardDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.LowerGradeReportDto;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class LowerGradeTerm2ReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final StudentAttributeService studentAttributeService;
  private final LowerGradeFirstTermReportCard lowerGradeFirstTermReportCard;
  private final LowerGradeOverallReportCard lowerGradeOverallReportCard;
  private static final String DPS_AEROCITY_SLUG = "del189476";
  private final SportsReportCard sportsReportCard;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return Optional.empty();
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("lower-grade-term2-report.xml");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug(), request.withMarks());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  public LowerGradeReportDto.Body buildBody(User user, String orgSlug, Boolean withMarks) {
    var student = user.getStudentInfo();
    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var tableMarks = buildTableMarks(student, withMarks, orgSlug);
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(
            tableMarks.firstTableMarks().getFirst().otdId());
    var termId = 2L;
    List<SportsReportCardDto.MainTable> physicalData =
        lowerGradeOverallReportCard.buildPhysicalData(user, termId);
    SportsReportCardDto.Overall physData =
        sportsReportCard.getLearningLevelByListOfGrades(physicalData);

    return LowerGradeReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(studentAttributeService.getDateOfBirthFormat(dateOfBirth))
        .className(student.getSection().getName())
        .orgSlug(orgSlug)
        .gradeSlug(student.getSection().getGradeSlug())
        .rollNumber(student.getClassRollNumber())
        .mothersName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .firstTable(
            buildFirstTable(
                tableMarks.firstTableMarks(), tableMarks.externalMarks(), student, withMarks))
        .secondTable(lowerGradeFirstTermReportCard.buildSecondTable(tableMarks.secondTableMarks()))
        .thirdTable(lowerGradeFirstTermReportCard.buildThirdTable(tableMarks.thirdTableMarks()))
        .fourthTable(buildFourthTable(physData))
        .attendance(lowerGradeOverallReportCard.buildAttendance(student.getId()))
        .gradeTable(LowerGradeReportDto.GradeTable.builder().title("Grade Scale").build())
        .gradingScale(
            testDefinition.getGradeScaleSlug() != null
                ? testDefinition.getGradeScaleSlug()
                : "4point")
        .build();
  }

  private List<LowerGradeReportDto.FourthTable> buildFourthTable(
      SportsReportCardDto.Overall physData) {
    return Collections.singletonList(
        LowerGradeReportDto.FourthTable.builder().term1Grade(physData.overallGrade()).build());
  }

  public LowerGradeReportDto.FirstTable buildFirstTable(
      List<LowerGradeReportDto.Marks> firstTableMarks,
      List<LowerGradeReportDto.Marks> externalMarks,
      Student student,
      Boolean withMarks) {
    var section = student.getSection();

    var reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlug(
            section.getOrganization(), section.getBoardSlug(), section.getGradeSlug());
    var configDetails =
        reportCardConfigs.get(1).getReportCardConfigDetails().stream()
            .sorted(Comparator.comparing(ReportCardConfigDetail::getSeqNo))
            .toList();

    var total =
        configDetails.stream()
            .filter(x -> x.getWeightage() != null)
            .mapToLong(ReportCardConfigDetail::getWeightage)
            .sum();
    List<String> subjectGradeSlugs =
        firstTableMarks.stream()
            .map(marks -> marks.subject() + "-" + marks.grade1())
            .collect(Collectors.toList());

    return LowerGradeReportDto.FirstTable.builder()
        .title("PART-I: SCHOLASTIC AREAS")
        .column1(lowerGradeFirstTermReportCard.constructColumn(configDetails.getFirst()))
        .column2(lowerGradeFirstTermReportCard.constructColumn(configDetails.get(1)))
        .column3(lowerGradeFirstTermReportCard.constructColumn(configDetails.get(2)))
        .column4(format("%s (%s)", "Total", total))
        .marks(firstTableMarks)
        .subjectGradeSlug(subjectGradeSlugs)
        .external(externalMarks)
        .totals(lowerGradeFirstTermReportCard.buildTotals(firstTableMarks, withMarks))
        .build();
  }

  private LowerGradeReportDto.TableMarks buildTableMarks(
      Student student, Boolean withMarks, String orgSlug) {

    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            List.of("t2"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var optionalData =
        new ArrayList<>(
            data.stream()
                .filter(
                    x ->
                        SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                            && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
                .toList());

    var coScholasticData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .toList();
    var coScholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    var gradeSlug = student.getSection().getGradeSlug();
    var sortedData =
        sortTable(
            buildTableMarks(scholasticDataList, withMarks, "", orgSlug),
            buildTableMarks(optionalData, withMarks, "", orgSlug),
            buildTableMarks(coScholasticData, false, "", orgSlug),
            buildTableMarks(coScholasticOptionalData, false, gradeSlug, orgSlug));
    return LowerGradeReportDto.TableMarks.builder()
        .firstTableMarks(sortedData.firstTableMarks())
        .externalMarks(sortedData.externalMarks())
        .secondTableMarks(sortedData.secondTableMarks())
        .thirdTableMarks(sortedData.thirdTableMarks())
        .build();
  }

  private LowerGradeReportDto.TableMarks sortTable(
      List<LowerGradeReportDto.Marks> firstTableMarks,
      List<LowerGradeReportDto.Marks> externalMarks,
      List<LowerGradeReportDto.Marks> secondTableMarks,
      List<LowerGradeReportDto.Marks> thirdTableMarks) {
    List<LowerGradeReportDto.Marks> firstTable = new ArrayList<>();
    List<LowerGradeReportDto.SecondTableMarks> secondTable = new ArrayList<>();
    List<LowerGradeReportDto.ThirdTableMarks> thirdTable = new ArrayList<>();
    List<LowerGradeReportDto.Marks> externalTable = new ArrayList<>();
    var sortedFirstTable =
        firstTableMarks.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportDto.Marks::seqNo))
            .toList();
    s: // learn.academyteacher.com
    for (int i = 0; i < sortedFirstTable.size(); i++) {
      LowerGradeReportDto.Marks mark = sortedFirstTable.get(i);
      firstTable.add(
          LowerGradeReportDto.Marks.builder()
              .sno(i + 1L)
              .pa1(mark.pa3())
              .pa2(mark.pa4())
              .hye(mark.ye())
              .term1total(mark.term1total())
              .subject(mark.subject())
              .overAllGrade(mark.overAllGrade())
              .overall(mark.overall())
              .overAllExamMarks(mark.overAllExamMarks())
              .overAllScored(mark.overAllScored())
              .otdId(mark.otdId())
              .build());
    }

    var sortedThirdTable =
        thirdTableMarks.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedThirdTable.size(); i++) {
      LowerGradeReportDto.Marks mark = sortedThirdTable.get(i);
      thirdTable.add(
          LowerGradeReportDto.ThirdTableMarks.builder()
              .sno(sortedFirstTable.size() + i + 1L)
              .subject(mark.subject())
              .term1grade(mark.grade1())
              .term1description(mark.overall())
              .build());
    }

    var sortedExternalTable =
        externalMarks.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedExternalTable.size(); i++) {
      LowerGradeReportDto.Marks mark = sortedExternalTable.get(i);
      externalTable.add(
          LowerGradeReportDto.Marks.builder()
              .sno(sortedFirstTable.size() + sortedThirdTable.size() + i + 1L)
              .pa1(mark.pa3())
              .pa2(mark.pa4())
              .hye(mark.ye())
              .term1total(mark.term1total())
              .overAllGrade(mark.overAllGrade())
              .subject(mark.subject())
              .overall(mark.overall())
              .build());
    }
    var sortedSecondTable =
        secondTableMarks.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedSecondTable.size(); i++) {
      LowerGradeReportDto.Marks mark = sortedSecondTable.get(i);
      var term1total = mark.term1total() == null ? mark.ye() : mark.term1total();
      secondTable.add(
          LowerGradeReportDto.SecondTableMarks.builder()
              .term1Grade(term1total)
              .subjectName(mark.subject())
              .build());
    }
    return LowerGradeReportDto.TableMarks.builder()
        .firstTableMarks(firstTable)
        .externalMarks(externalTable)
        .secondTableMarks(secondTable)
        .thirdTableMarks(thirdTable)
        .build();
  }

  public List<LowerGradeReportDto.Marks> buildTableMarks(
      List<LowerGradeReportCardData> reportCardData,
      Boolean withMarks,
      String gradeSlug,
      String orgSlug) {
    List<LowerGradeReportDto.Marks> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));
    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var pa3 = lowerGradeFirstTermReportCard.getMarks("pa3", scholasticData);
          var pa4 = lowerGradeFirstTermReportCard.getMarks("pa4", scholasticData);
          var ye = lowerGradeFirstTermReportCard.getMarks("ye", scholasticData);

          var term1Total = lowerGradeFirstTermReportCard.sumMarks(pa3, pa4, ye);
          var totalMarks =
              scholasticData.stream()
                  .filter(x -> x.getTotalMarks() != null)
                  .mapToDouble(LowerGradeReportCardData::getTotalMarks)
                  .sum();
          var isCoScholasticSubject =
              SubjectsCategoryEnum.CO_SCHOLASTIC
                  .name()
                  .equals(scholasticData.getFirst().getCategory());
          var isOptional =
              SubjectsTypeEnum.OPTIONAL.name().equals(scholasticData.getFirst().getType());
          var offlineTestDefinition =
              offlineTestScheduleService.validateOfflineTestDefinition(
                  scholasticData.getFirst().getOtdId());
          String grade = null;
          String description = null;
          String gradeLevel = null;
          if (isCoScholasticSubject && isOptional) {
            if (gradeSlug.equals("i")) {
              gradeLevel = "class1-grades";
            } else if (gradeSlug.equals("ii")) {
              gradeLevel = "class2-grades";
            }
            if (subject.equalsIgnoreCase("Numeracy Fluency Test")) {
              grade =
                  lowerGradeFirstTermReportCard.calculateCoScholasticGrade(
                      scholasticData.getFirst().getMarks(), "4point");
              description =
                  lowerGradeFirstTermReportCard.getGradeContent(
                      "numeracy-fluency-test", gradeLevel, grade);
            } else {
              grade =
                  lowerGradeFirstTermReportCard.calculateCoScholasticGrade(
                      scholasticData.getFirst().getMarks(), "6point");
              description =
                  lowerGradeFirstTermReportCard.getGradeContent(
                      "reading-fluency-test", gradeLevel, grade);
            }
          }
          marksList.add(
              LowerGradeReportDto.Marks.builder()
                  .pa3(
                      Boolean.TRUE.equals(withMarks)
                          ? ((DPS_AEROCITY_SLUG.equals(orgSlug)
                                  && (Boolean.TRUE.equals(isOptional)))
                              ? lowerGradeFirstTermReportCard.calculateMarks("pa3", scholasticData)
                              : String.valueOf(
                                  lowerGradeFirstTermReportCard.getMarks("pa3", scholasticData)))
                          : lowerGradeFirstTermReportCard.calculateMarks("pa3", scholasticData))
                  .pa4(
                      Boolean.TRUE.equals(withMarks)
                          ? ((DPS_AEROCITY_SLUG.equals(orgSlug)
                                  && (Boolean.TRUE.equals(isOptional)))
                              ? lowerGradeFirstTermReportCard.calculateMarks("pa4", scholasticData)
                              : String.valueOf(
                                  lowerGradeFirstTermReportCard.getMarks("pa4", scholasticData)))
                          : lowerGradeFirstTermReportCard.calculateMarks("pa4", scholasticData))
                  .ye(
                      Boolean.TRUE.equals(withMarks)
                          ? ((DPS_AEROCITY_SLUG.equals(orgSlug)
                                  && (Boolean.TRUE.equals(isOptional)))
                              ? lowerGradeFirstTermReportCard.calculateMarks("ye", scholasticData)
                              : String.valueOf(
                                  lowerGradeFirstTermReportCard.getMarks("ye", scholasticData)))
                          : lowerGradeFirstTermReportCard.calculateMarks("ye", scholasticData))
                  .term1total(
                      Boolean.TRUE.equals(withMarks)
                          ? String.format("%.1f", term1Total)
                          : lowerGradeFirstTermReportCard.getTerm1Total(
                              isCoScholasticSubject,
                              isOptional,
                              term1Total,
                              offlineTestDefinition.getGradeScaleSlug(),
                              totalMarks))
                  .overAllScored(term1Total)
                  .overAllExamMarks(totalMarks)
                  .subject(subject)
                  .overAllGrade(
                      lowerGradeFirstTermReportCard.getTerm1Total(
                          isCoScholasticSubject,
                          isOptional,
                          term1Total,
                          offlineTestDefinition.getGradeScaleSlug(),
                          totalMarks))
                  .seqNo(scholasticData.get(0).getSeqNo())
                  .otdId(scholasticData.getFirst().getOtdId())
                  .grade1(grade)
                  .overall(description)
                  .build());
        });
    return marksList;
  }
}
