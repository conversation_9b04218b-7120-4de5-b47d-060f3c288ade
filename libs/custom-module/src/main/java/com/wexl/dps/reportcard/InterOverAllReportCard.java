package com.wexl.dps.reportcard;

import com.wexl.dps.dto.InterReportCardData;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.InterOverAllReportDto;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentRepository;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class InterOverAllReportCard extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final OfflineTestScheduleStudentRepository offlineTestScheduleStudentRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final TermAssessmentRepository termAssessmentRepository;

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("inter-overall-report.xml");
  }

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  public InterOverAllReportDto.Body buildBody(User user, String orgSlug) {
    var student = user.getStudentInfo();
    var guardians = student.getGuardians();
    var mother =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.MOTHER)).findAny();
    var father =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.FATHER)).findAny();
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var tableMarks = buildTableMarks(student, orgSlug);
    return InterOverAllReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(null))
        .className(student.getSection().getName())
        .rollNumber(student.getClassRollNumber())
        .orgSlug(orgSlug)
        .gradeSlug(student.getSection().getGradeSlug())
        .mothersName(
            mother
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .fathersName(
            father
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .firstTable(buildFirstTable(tableMarks.firstTableMarks()))
        .secondTable(buildSecondTable(tableMarks.secondTableMarks()))
        .attendance(buildAttendance(student.getId()))
        .build();
  }

  private InterOverAllReportDto.FirstTable buildFirstTable(
      List<InterOverAllReportDto.FirstTableMarks> firstTableMarks) {
    return InterOverAllReportDto.FirstTable.builder()
        .title("PART-I: SCHOLASTIC AREAS")
        .marks(firstTableMarks)
        .totals(buildTotals(firstTableMarks))
        .build();
  }

  private InterOverAllReportDto.SecondTable buildSecondTable(
      List<InterOverAllReportDto.SecondTableMarks> secondTables) {
    return InterOverAllReportDto.SecondTable.builder()
        .title("PART-I: SCHOLASTIC AREAS")
        .marks(secondTables)
        .build();
  }

  private InterOverAllReportDto.Totals buildTotals(
      List<InterOverAllReportDto.FirstTableMarks> firstTableMarks) {
    long totalMarksScored =
        firstTableMarks.stream()
            .map(InterOverAllReportDto.FirstTableMarks::totalMarksScored)
            .filter(Objects::nonNull)
            .mapToLong(Long::longValue)
            .sum();

    long totalExamMarks =
        firstTableMarks.stream()
            .map(InterOverAllReportDto.FirstTableMarks::totalExamMarks)
            .filter(Objects::nonNull)
            .mapToLong(Long::longValue)
            .sum();

    double overallPercentage =
        (totalExamMarks == 0) ? 0 : Math.round(((double) totalMarksScored / totalExamMarks) * 100);

    String grade =
        (totalExamMarks == 0)
            ? "N/A"
            : pointScaleEvaluator.evaluate("4point", BigDecimal.valueOf(overallPercentage));

    return InterOverAllReportDto.Totals.builder()
        .annualExam(0L)
        .total(totalMarksScored)
        .grade(grade)
        .overallPercentage(overallPercentage)
        .build();
  }

  private InterOverAllReportDto.TableMarks buildTableMarks(Student student, String orgSlug) {
    List<InterReportCardData> data;
    List<InterReportCardData> scholasticDataList;
    List<InterReportCardData> coScholasticData;
    data = reportCardConfigDataRepository.getInterReportCard(student.getId(), orgSlug);
    scholasticDataList =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name())
                        && !x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    coScholasticData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && !x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();

    return InterOverAllReportDto.TableMarks.builder()
        .firstTableMarks(sortTable(buildTableMarks(scholasticDataList)))
        .secondTableMarks(buildSecondTableMarks(buildTableMarks(coScholasticData)))
        .build();
  }

  private List<InterOverAllReportDto.SecondTableMarks> buildSecondTableMarks(
      List<InterOverAllReportDto.FirstTableMarks> secondTableMarks) {
    List<InterOverAllReportDto.SecondTableMarks> marks = new ArrayList<>();
    secondTableMarks.forEach(
        mark ->
            marks.add(
                InterOverAllReportDto.SecondTableMarks.builder()
                    .subjectName(mark.subject())
                    .term1Grade(calculateGrade(mark.totalExamMarks(), mark.totalMarksScored()))
                    .build()));

    return marks;
  }

  private String calculateGrade(Long marks, Long totalMarks) {
    return marks == null
        ? null
        : pointScaleEvaluator.evaluate("4point", BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  public List<InterOverAllReportDto.FirstTableMarks> buildTableMarks(
      List<InterReportCardData> scholasticDataList) {
    List<InterOverAllReportDto.FirstTableMarks> marksList = new ArrayList<>();
    var subjects =
        scholasticDataList.stream()
            .sorted(Comparator.comparingLong(InterReportCardData::getSeqNo))
            .map(InterReportCardData::getSubjectName)
            .distinct()
            .toList();

    for (String subject : subjects) {
      var subjectData =
          scholasticDataList.stream()
              .filter(data -> data.getSubjectName().equals(subject))
              .toList();
      var theoryMarks = getMarks("Theory", subjectData);
      var practicalMarks = getMarks("Practical", subjectData);
      var totalMarks =
          (theoryMarks == null ? 0L : theoryMarks) + (practicalMarks == null ? 0L : practicalMarks);
      var totalExamMarks = subjectData.stream().mapToLong(InterReportCardData::getTotalMarks).sum();

      marksList.add(
          InterOverAllReportDto.FirstTableMarks.builder()
              .subject(subject)
              .seqNo(subjectData.get(0).getSeqNo())
              .theoryMarks(calculateMarks("Theory", subjectData))
              .practicalMarks(calculateMarks("Practical", subjectData))
              .highestMarks(subjectData.get(0).getHighestMarks().toString())
              .totalMarksScored(totalMarks)
              .total(String.valueOf(totalMarks))
              .totalExamMarks(totalExamMarks)
              .build());
    }
    return marksList;
  }

  private String calculateMarks(String assessmentSlug, List<InterReportCardData> subjectData) {
    return subjectData.stream()
        .filter(data -> data.getAssessmentSlug().equalsIgnoreCase(assessmentSlug))
        .findFirst()
        .map(
            data -> {
              if (data.getIsAttended() == null) {
                return null;
              } else if (data.getIsAttended().equals("False")) {
                return "A";
              }
              return data.getMarks() != null ? data.getMarks().toString() : null;
            })
        .orElse(null);
  }

  private Long getMarks(String assessmentSlug, List<InterReportCardData> subjectData) {
    return subjectData.stream()
        .filter(data -> data.getAssessmentSlug().equalsIgnoreCase(assessmentSlug))
        .map(InterReportCardData::getMarks)
        .findFirst()
        .map(Double::longValue)
        .orElse(null);
  }

  private List<InterOverAllReportDto.FirstTableMarks> sortTable(
      List<InterOverAllReportDto.FirstTableMarks> firstTableMarks) {
    List<InterOverAllReportDto.FirstTableMarks> firstTable = new ArrayList<>();
    var sortedFirstTable =
        firstTableMarks.stream()
            .sorted(Comparator.comparingLong(InterOverAllReportDto.FirstTableMarks::seqNo))
            .toList();
    for (int i = 0; i < sortedFirstTable.size(); i++) {
      InterOverAllReportDto.FirstTableMarks mark = sortedFirstTable.get(i);
      firstTable.add(
          InterOverAllReportDto.FirstTableMarks.builder()
              .sNo(i + 1L)
              .subject(mark.subject())
              .theoryMarks(mark.theoryMarks())
              .practicalMarks(mark.practicalMarks())
              .highestMarks(mark.highestMarks())
              .total(mark.total())
              .build());
    }

    return firstTable;
  }

  private InterOverAllReportDto.Attendance buildAttendance(long studentId) {
    var termAssessment = termAssessmentRepository.findBySlug("hye");
    if (termAssessment.isEmpty()) {
      return InterOverAllReportDto.Attendance.builder().build();
    }
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, termAssessment.get().getId());
    if (studentAttendance.isEmpty()) {
      return InterOverAllReportDto.Attendance.builder().build();
    }

    var testDefinition = studentAttendance.get().getOfflineTestDefinition();
    if (testDefinition == null) {
      throw new IllegalArgumentException("Test definition not found for studentId: " + studentId);
    }
    String totalAttendanceDays = testDefinition.getTotalAttendanceDays();
    Long daysPresent = studentAttendance.get().getPresentDays();
    if (totalAttendanceDays == null || daysPresent == null) {
      return InterOverAllReportDto.Attendance.builder().build();
    }

    Double attendancePercentage = null;
    double totalDays = Double.parseDouble(totalAttendanceDays);
    if (totalDays > 0.0) {
      attendancePercentage = (double) Math.round((daysPresent.doubleValue() / totalDays) * 100);
    }
    return InterOverAllReportDto.Attendance.builder()
        .workingDays(Long.valueOf(totalAttendanceDays))
        .daysPresent(daysPresent)
        .attendancePercentage(attendancePercentage)
        .remarks(studentAttendance.get().getRemarks())
        .build();
  }
}
