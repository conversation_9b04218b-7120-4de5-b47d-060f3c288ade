package com.wexl.dps.reportcard;

import static java.lang.String.format;

import com.wexl.dps.dto.UpperGradeReportDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UpperGradeSecondTermReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final UpperGradeFirstTermReportCard upperGradeFirstTermReportCard;
  private final UpperGradeOverAllReportCard upperGradeOverAllReportCard;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final StudentAttributeService studentAttributeService;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final LowerGradeOverallReportCard lowerGradeOverallReportCard;
  private static final String FALSE = "false";

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("upper-grade-term2-report.xml");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug(), request);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  public UpperGradeReportDto.Body buildBody(
      User user, String orgSlug, ReportCardDto.Request request) {
    var student = user.getStudentInfo();

    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var tableMarks = buildTableMarks(student);
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(
            tableMarks.firstTableMarks().getFirst().otdId());
    return UpperGradeReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(studentAttributeService.getDateOfBirthFormat(dateOfBirth))
        .className(student.getSection().getName())
        .rollNumber(student.getClassRollNumber())
        .orgSlug(orgSlug)
        .gradeSlug(student.getSection().getGradeSlug())
        .mothersName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .firstTable(
            buildFirstTable(tableMarks.firstTableMarks(), tableMarks.externalMarks(), student))
        .secondTable(upperGradeFirstTermReportCard.buildSecondTable(tableMarks.secondTableMarks()))
        .attendance(
            upperGradeOverAllReportCard.buildAttendance(
                student.getId(), request.offlineTestDefinitionId()))
        .gradeTable(UpperGradeReportDto.GradeTable.builder().title("Grade Scale").build())
        .gradingScale(
            testDefinition.getGradeScaleSlug() != null
                ? testDefinition.getGradeScaleSlug()
                : "8point")
        .build();
  }

  public UpperGradeReportDto.FirstTable buildFirstTable(
      List<UpperGradeReportDto.Marks> firstTableMarks,
      List<UpperGradeReportDto.Marks> externalMarks,
      Student student) {
    var section = student.getSection();

    var reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlug(
            section.getOrganization(), section.getBoardSlug(), section.getGradeSlug());
    var configDetails =
        reportCardConfigs.get(1).getReportCardConfigDetails().stream()
            .sorted(Comparator.comparing(ReportCardConfigDetail::getSeqNo))
            .toList();

    return UpperGradeReportDto.FirstTable.builder()
        .title("PART-I: SCHOLASTIC AREAS")
        .column1(constructColumn(configDetails.getFirst()))
        .column2(constructColumn(configDetails.get(1)))
        .column3(constructColumn(configDetails.get(2)))
        .column4(constructColumn(configDetails.get(3)))
        .marks(firstTableMarks)
        .external(externalMarks)
        .totals(upperGradeFirstTermReportCard.buildTotals(firstTableMarks))
        .build();
  }

  private String constructColumn(ReportCardConfigDetail configDetail) {
    return Objects.isNull(configDetail)
        ? null
        : format(
            "%s (%s)", configDetail.getTermAssessment().getName(), configDetail.getWeightage());
  }

  private UpperGradeReportDto.TableMarks buildTableMarks(Student student) {

    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            Collections.singletonList("t2"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var optionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();

    var coScholasticData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .toList();

    var sortedData =
        sortTable(
            buildTableMarks(scholasticDataList),
            buildTableMarks(optionalData),
            buildTableMarks(coScholasticData));
    return UpperGradeReportDto.TableMarks.builder()
        .firstTableMarks(sortedData.firstTableMarks())
        .externalMarks(sortedData.externalMarks())
        .secondTableMarks(sortedData.secondTableMarks())
        .build();
  }

  public List<UpperGradeReportDto.Marks> buildTableMarks(
      List<LowerGradeReportCardData> reportCardData) {
    List<UpperGradeReportDto.Marks> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var pt =
              upperGradeOverAllReportCard.getMarks(
                  List.of("pt(pt3+pt4)", "pt2-cra2"), scholasticData);
          var nb2 = upperGradeOverAllReportCard.getMarks(List.of("nb2"), scholasticData);
          var se2 = upperGradeOverAllReportCard.getMarks(List.of("se2"), scholasticData);
          var ye = upperGradeOverAllReportCard.getMarks(List.of("ye"), scholasticData);

          var term1Total = upperGradeFirstTermReportCard.sumMarks(pt, nb2, se2, ye);
          var termMaxMarks = 100d;
          var offlineTestDefinition =
              offlineTestScheduleService.validateOfflineTestDefinition(
                  scholasticData.getFirst().getOtdId());
          String grade =
              upperGradeFirstTermReportCard.calculateGrade(
                  term1Total, termMaxMarks, offlineTestDefinition.getGradeScaleSlug());
          double term1TotalDouble = Double.parseDouble(String.format("%.1f", term1Total));

          marksList.add(
              UpperGradeReportDto.Marks.builder()
                  .pt(upperGradeFirstTermReportCard.buildMarks(scholasticData, pt))
                  .nb1(upperGradeFirstTermReportCard.buildMarks(scholasticData, se2))
                  .se1(upperGradeFirstTermReportCard.buildMarks(scholasticData, nb2))
                  .hye(upperGradeFirstTermReportCard.buildMarks(scholasticData, ye))
                  .grade1(grade)
                  .marksObtained1(term1TotalDouble)
                  .subject(subject)
                  .seqNo(scholasticData.get(0).getSeqNo())
                  .otdId(scholasticData.getFirst().getOtdId())
                  .build());
        });
    return marksList;
  }

  private UpperGradeReportDto.TableMarks sortTable(
      List<UpperGradeReportDto.Marks> firstTableMarks,
      List<UpperGradeReportDto.Marks> externalMarks,
      List<UpperGradeReportDto.Marks> secondTableMarks) {
    List<UpperGradeReportDto.Marks> firstTable = new ArrayList<>();
    List<UpperGradeReportDto.SecondTableMarks> secondTable = new ArrayList<>();
    List<UpperGradeReportDto.Marks> externalTable = new ArrayList<>();
    var sortedFirstTable =
        firstTableMarks.stream()
            .sorted(Comparator.comparingLong(UpperGradeReportDto.Marks::seqNo))
            .toList();
    s: // learn.academyteacher.com
    for (int i = 0; i < sortedFirstTable.size(); i++) {
      UpperGradeReportDto.Marks mark = sortedFirstTable.get(i);
      firstTable.add(
          UpperGradeReportDto.Marks.builder()
              .sno(i + 1L)
              .pt(mark.pt())
              .nb1(mark.nb1())
              .se1(mark.se1())
              .hye(mark.hye())
              .marksObtained1(mark.marksObtained1())
              .grade1(mark.grade1())
              .subject(mark.subject())
              .overAllGrade(mark.overAllGrade())
              .overAllScored(mark.overAllScored())
              .otdId(mark.otdId())
              .build());
    }

    var sortedExternalTable =
        externalMarks.stream()
            .sorted(Comparator.comparingLong(UpperGradeReportDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedExternalTable.size(); i++) {
      UpperGradeReportDto.Marks mark = sortedExternalTable.get(i);
      externalTable.add(
          UpperGradeReportDto.Marks.builder()
              .sno(sortedFirstTable.size() + i + 1L)
              .pt(mark.pt())
              .nb1(mark.nb1())
              .se1(mark.se1())
              .hye(mark.hye())
              .grade2(mark.grade1())
              .subject(mark.subject())
              .marksObtained2(mark.marksObtained1())
              .overAllGrade(mark.overAllGrade())
              .build());
    }
    var sortedSecondTable =
        secondTableMarks.stream()
            .sorted(Comparator.comparingLong(UpperGradeReportDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedSecondTable.size(); i++) {
      UpperGradeReportDto.Marks mark = sortedSecondTable.get(i);
      var hye = mark.hye();
      String grade = null;

      if (hye != null && !hye.equals("AB") && !hye.equals("PA") && !hye.equals("ML")) {
        grade = offlineTestScheduleService.getGrade(BigDecimal.valueOf(Double.parseDouble(hye)));
      }

      secondTable.add(
          UpperGradeReportDto.SecondTableMarks.builder()
              .term1Grade(Objects.isNull(grade) ? hye : grade)
              .subjectName(mark.subject())
              .build());
    }
    return UpperGradeReportDto.TableMarks.builder()
        .firstTableMarks(firstTable)
        .externalMarks(externalTable)
        .secondTableMarks(secondTable)
        .build();
  }

  public List<ReportCardConfigDto.GradeAndPercentageResponse> getAGradeResponse(
      List<Student> students) {

    List<ReportCardConfigDto.GradeAndPercentageResponse> responseList = new ArrayList<>();
    students.forEach(
        student -> {
          try {
            var tableMarks = buildTableMarks(student);
            var firstTable =
                buildFirstTable(tableMarks.firstTableMarks(), tableMarks.externalMarks(), student);

            if (firstTable.totals().grade().equals("A1")) {

              var userInfo = student.getUserInfo();
              var name = userInfo.getFirstName() + " " + userInfo.getLastName();
              responseList.add(
                  ReportCardConfigDto.GradeAndPercentageResponse.builder()
                      .grade(firstTable.totals().grade())
                      .studentName(name)
                      .studentId(student.getId())
                      .sectionName(student.getSection().getName())
                      .build());
            }

          } catch (Exception ignored) {
          }
        });
    return responseList;
  }

  public List<ReportCardConfigDto.GradeAndPercentageResponse> getAGradeAllSubjectsSummary(
      List<Student> students) {
    List<ReportCardConfigDto.GradeAndPercentageResponse> responseList = new ArrayList<>();
    students.forEach(
        student -> {
          try {
            var tableMarks = buildTableMarks(student);
            var firstTable =
                buildFirstTable(tableMarks.firstTableMarks(), tableMarks.externalMarks(), student);
            boolean allSubjectsA1 =
                firstTable.marks().stream()
                    .allMatch(subject -> "A1".equals(subject.overAllGrade()));

            if (allSubjectsA1) {
              var userInfo = student.getUserInfo();
              var name = userInfo.getFirstName() + " " + userInfo.getLastName();

              responseList.add(
                  ReportCardConfigDto.GradeAndPercentageResponse.builder()
                      .grade(firstTable.totals().grade())
                      .studentName(name)
                      .studentId(student.getId())
                      .sectionName(student.getSection().getName())
                      .build());
            }

          } catch (Exception ex) {
          }
        });
    return responseList;
  }
}
