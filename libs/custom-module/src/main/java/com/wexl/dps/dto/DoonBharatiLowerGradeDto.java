package com.wexl.dps.dto;

import java.util.List;
import lombok.Builder;

public record DoonBharatiLowerGradeDto() {

  @Builder
  public record Header(
      String schoolName,
      String sessionStart,
      String sessionEnd,
      String orgSlug,
      String className,
      String gradeSlug,
      String sectionName,
      String studentName,
      String fatherName,
      String dateOfBirth,
      String admissionNo,
      String address,
      String mobileNo,
      String rollNo) {}

  @Builder
  public record Body(
      FirstTableMarks firstTableMarks,
      List<RemarksTable> remarksTable,
      SecondTable secondTable,
      Boolean isParticipatedInInternal,
      Boolean isParticipated,
      Long totalWorkingDays,
      Long totalPresentDays,
      String height,
      String weight,
      String olympiadSubjects,
      String internalOlympiadSubjects,
      String term,
      String overallRemark,
      Boolean promotedToNextClass) {}

  @Builder
  public record FirstTableMarks(List<Marks> marks) {}

  @Builder
  public record Marks(
      String subjectName,
      String launchpadLearningGrade,
      Double portFolio,
      String competitiveDevelopment,
      Double ma,
      Double midMasteryTriumph,
      Double total,
      String grade) {}

  @Builder
  public record RemarksTable(String midMastery, String yearlyKnowledgeVoyage) {}

  @Builder
  public record SecondTable(List<SecondTableMarks> secondTableMarks) {}

  @Builder
  public record SecondTableMarks(
      String subjectName, String midMasteryTriumph, String yearlyKnowledgeVoyage) {}
}
