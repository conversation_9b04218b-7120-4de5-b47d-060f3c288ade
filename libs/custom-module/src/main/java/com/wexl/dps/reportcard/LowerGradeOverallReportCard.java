package com.wexl.dps.reportcard;

import static java.lang.String.format;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.dps.dto.SportsReportCardDto;
import com.wexl.dps.learningmilestones.model.LmrCategory;
import com.wexl.dps.learningmilestones.model.LmrCategoryGrade;
import com.wexl.dps.learningmilestones.model.LmrCategoryType;
import com.wexl.dps.learningmilestones.repository.CategoryGradeAttributeRepository;
import com.wexl.dps.learningmilestones.repository.LmrCategoryGradeRepository;
import com.wexl.dps.learningmilestones.repository.LmrCategoryRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.LowerGradeReportDto;
import com.wexl.retail.offlinetest.dto.LowerGradeReportDto.Marks;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.OfflineTestScheduleStudentAttendance;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.model.SubjectsMetadataStudents;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.subjects.repository.SubjectsMetadataStudentsRepository;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class LowerGradeOverallReportCard extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final StudentAttributeService studentAttributeService;
  private final SportsReportCard sportsReportCard;
  private final SubjectsMetadataStudentsRepository subjectsMetadataStudentsRepository;
  private final LmrCategoryRepository lmrCategoryRepository;
  private final LmrCategoryGradeRepository lmrCategoryGradeRepository;
  private final CategoryGradeAttributeRepository categoryGradeAttributeRepository;

  @Value("classpath:dps-grades-description.json")
  private Resource dpsGradesDescription;

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug(), request.withMarks());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("lower-grade-overall-report.xml");
  }

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  public LowerGradeReportDto.Body buildBody(User user, String orgSlug, Boolean withMarks) {
    var student = user.getStudentInfo();
    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var tableMarks = buildTableMarks(student, withMarks);
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(
            tableMarks.firstTableMarks().getFirst().otdId());
    List<Long> termIds = List.of(1L, 2L);
    List<SportsReportCardDto.Overall> physData = new ArrayList<>();
    for (Long termId : termIds) {
      List<SportsReportCardDto.MainTable> physicalData = buildPhysicalData(user, termId);
      physData.add(sportsReportCard.getLearningLevelByListOfGrades(physicalData));
    }
    return LowerGradeReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(studentAttributeService.getDateOfBirthFormat(dateOfBirth))
        .className(student.getSection().getName())
        .orgSlug(orgSlug)
        .gradeSlug(student.getSection().getGradeSlug())
        .boardSlug(student.getSection().getBoardSlug())
        .rollNumber(student.getClassRollNumber())
        .mothersName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .firstTable(
            buildFirstTable(
                tableMarks.firstTableMarks(), tableMarks.externalMarks(), student, withMarks))
        .secondTable(buildSecondTable(tableMarks.secondTableMarks()))
        .thirdTable(buildThirdTable(tableMarks.thirdTableMarks()))
        .fourthTable(buildFourthTable(physData))
        .attendance(buildAttendance(student.getId()))
        .gradeTable(LowerGradeReportDto.GradeTable.builder().title("Grade Scale").build())
        .gradingScale(
            testDefinition.getGradeScaleSlug() != null
                ? testDefinition.getGradeScaleSlug()
                : "4point")
        .build();
  }

  private List<LowerGradeReportDto.FourthTable> buildFourthTable(
      List<SportsReportCardDto.Overall> physData) {
    return physData.stream()
        .map(
            data ->
                LowerGradeReportDto.FourthTable.builder().term1Grade(data.overallGrade()).build())
        .toList();
  }

  public List<SportsReportCardDto.MainTable> buildPhysicalData(User user, Long termId) {
    var student = user.getStudentInfo();
    List<SubjectsMetadataStudents> subjectsMetadataStudentsList =
        subjectsMetadataStudentsRepository.findByStudentId(student.getId());
    List<SubjectsMetaData> subjectsMetaData =
        subjectsMetadataStudentsList.stream()
            .filter(
                stu ->
                    stu.getSubjectsMetaData() != null
                        && "sports".equals(stu.getSubjectsMetaData().getWexlSubjectSlug()))
            .map(stu -> stu.getSubjectsMetaData())
            .collect(Collectors.toList());
    if (subjectsMetaData.isEmpty()) {
      return Collections.emptyList();
    }
    List<LmrCategoryGrade> lmrCategoryGrades =
        lmrCategoryGradeRepository.getAllByGradeSlugAndSubjectMetadataIdAndTerm(
            student.getSection().getGradeSlug(), subjectsMetaData.getFirst().getId(), termId);
    var lmrCategoryIds =
        lmrCategoryGrades.stream().map(LmrCategoryGrade::getLmrCategoryId).toList();
    var lmrCategories =
        lmrCategoryRepository.findByIdInAndType(
            new ArrayList<>(lmrCategoryIds), LmrCategoryType.EY_REPORT);
    List<SportsReportCardDto.MainTable> mainTables = new ArrayList<>();
    for (LmrCategory lmrCategory :
        lmrCategories.stream().sorted(Comparator.comparing(LmrCategory::getSequence)).toList()) {
      var lmrGradeIds =
          lmrCategoryGrades.stream()
              .filter(lcg -> lcg.getLmrCategoryId().equals(lmrCategory.getId()))
              .map(LmrCategoryGrade::getId)
              .toList();
      var lmrCategoryAttributes =
          categoryGradeAttributeRepository.findByLmrCategoryGradeIdIn(lmrGradeIds);
      mainTables.add(
          sportsReportCard.buildMainTableDetails(user, lmrCategory, lmrCategoryAttributes, termId));
    }
    return mainTables;
  }

  private LowerGradeReportDto.ThirdTable buildThirdTable(
      List<LowerGradeReportDto.ThirdTableMarks> thirdTableMarks) {
    return LowerGradeReportDto.ThirdTable.builder().marks(thirdTableMarks).build();
  }

  private LowerGradeReportDto.FirstTable buildFirstTable(
      List<Marks> firstTableMarks, List<Marks> externalMarks, Student student, Boolean withMarks) {
    var section = student.getSection();

    var reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlug(
            section.getOrganization(), section.getBoardSlug(), section.getGradeSlug());
    var configDetails =
        reportCardConfigs.getFirst().getReportCardConfigDetails().stream()
            .sorted(Comparator.comparing(ReportCardConfigDetail::getSeqNo))
            .toList();

    var total =
        configDetails.stream()
            .filter(x -> x.getWeightage() != null)
            .mapToLong(ReportCardConfigDetail::getWeightage)
            .sum();
    List<String> subjectGradeSlugs =
        firstTableMarks.stream()
            .map(marks -> marks.subject() + "-" + marks.grade1())
            .collect(Collectors.toList());

    return LowerGradeReportDto.FirstTable.builder()
        .title("PART-I: SCHOLASTIC AREAS")
        .column1(constructColumn(configDetails.getFirst()))
        .column2(constructColumn(configDetails.get(1)))
        .column3(constructColumn(configDetails.get(2)))
        .column4(format("%s (%s)", "Total", total))
        .marks(firstTableMarks)
        .subjectGradeSlug(subjectGradeSlugs)
        .external(externalMarks)
        .totals(buildTotals(firstTableMarks, withMarks))
        .build();
  }

  private String constructColumn(ReportCardConfigDetail configDetail) {
    return Objects.isNull(configDetail)
        ? null
        : format(
            "%s (%s)", configDetail.getTermAssessment().getName(), configDetail.getWeightage());
  }

  private LowerGradeReportDto.SecondTable buildSecondTable(
      List<LowerGradeReportDto.SecondTableMarks> marks) {
    return LowerGradeReportDto.SecondTable.builder()
        .title("PART-II: Co-Scholastic Areas[on a 3-point(A to C)grading scale]")
        .marks(marks)
        .build();
  }

  private LowerGradeReportDto.TableMarks buildTableMarks(Student student, Boolean withMarks) {

    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            List.of("t1", "t2"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);

    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name())
                        && !x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    var coScholasticData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && !x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    var optionalData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equalsIgnoreCase(SubjectsCategoryEnum.SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    var coScholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equalsIgnoreCase(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    String gradeSlug = student.getSection().getGradeSlug();
    var sortedData =
        sortTable(
            buildTableMarks(scholasticDataList, "", withMarks),
            buildTableMarks(optionalData, "", withMarks),
            buildTableMarks(coScholasticData, "", null),
            buildTableMarks(coScholasticOptionalData, gradeSlug, null));
    return LowerGradeReportDto.TableMarks.builder()
        .firstTableMarks(sortedData.firstTableMarks())
        .externalMarks(sortedData.externalMarks())
        .secondTableMarks(sortedData.secondTableMarks())
        .thirdTableMarks(sortedData.thirdTableMarks())
        .build();
  }

  public List<Marks> buildTableMarks(
      List<LowerGradeReportCardData> reportCardData, String gradeSlug, Boolean withMarks) {
    List<Marks> marksList = new ArrayList<>();

    Boolean validateWithMarks = Boolean.TRUE.equals(withMarks);
    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));
    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var pa1 = getMarks("pa1", scholasticData);
          var pa2 = getMarks("pa2", scholasticData);
          var hye = getMarks("hye", scholasticData);
          var pa3 = getMarks("pa3", scholasticData);
          var pa4 = getMarks("pa4", scholasticData);
          var ye = getMarks("ye", scholasticData);

          var term1Total = Double.parseDouble(String.format("%.1f", sumMarks(pa1, pa2, hye)));
          var term2Total = Double.parseDouble(String.format("%.1f", sumMarks(pa3, pa4, ye)));
          double term1Rounded =
              new BigDecimal(term1Total).setScale(0, RoundingMode.HALF_UP).intValue();
          double term2Rounded =
              new BigDecimal(term2Total).setScale(0, RoundingMode.HALF_UP).intValue();

          var termMaxMarks = 50d;
          var paMaxMarks = 20d;
          var hyeMaxMarks = 80d;
          var overAllMaxMarks = paMaxMarks + hyeMaxMarks;
          var paTotal = sumMarks(pa1, pa2, pa3, pa4);
          var hyeTotal = sumMarks(hye, ye);
          var overall = sumMarks(paTotal, hyeTotal);

          String grade1 = calculateGrades(term1Total, termMaxMarks);
          String grade2 = calculateGrades(term2Total, termMaxMarks);

          var totalMarks =
              scholasticData.stream()
                  .filter(x -> x.getTotalMarks() != null)
                  .mapToDouble(LowerGradeReportCardData::getTotalMarks)
                  .sum();
          var isCoScholasticSubject =
              SubjectsCategoryEnum.CO_SCHOLASTIC
                  .name()
                  .equals(scholasticData.getFirst().getCategory());
          var isOptional =
              SubjectsTypeEnum.OPTIONAL.name().equals(scholasticData.getFirst().getType());
          var offlineTestDefinition =
              offlineTestScheduleService.validateOfflineTestDefinition(
                  scholasticData.getFirst().getOtdId());
          String term2grade = null;
          String term2description = null;
          String gradeLevel = null;
          if (isCoScholasticSubject && isOptional) {
            if (gradeSlug.equals("i")) {
              gradeLevel = "class1-grades";
            } else if (gradeSlug.equals("ii")) {
              gradeLevel = "class2-grades";
            }
            if (subject.equalsIgnoreCase("Numeracy Fluency Test")) {
              term2grade =
                  calculateCoScholasticGrade(scholasticData.getFirst().getMarks(), "4point");
              term2description = getGradeContent("numeracy-fluency-test", gradeLevel, term2grade);
            } else {
              term2grade =
                  calculateCoScholasticGrade(scholasticData.getFirst().getMarks(), "6point");
              term2description = getGradeContent("reading-fluency-test", gradeLevel, term2grade);
            }
          }
          marksList.add(
              Marks.builder()
                  .pa1(calculateMarks("pa1", scholasticData, validateWithMarks))
                  .pa2(calculateMarks("pa2", scholasticData, validateWithMarks))
                  .pa3(calculateMarks("pa3", scholasticData, validateWithMarks))
                  .pa4(calculateMarks("pa4", scholasticData, validateWithMarks))
                  .hye(calculateMarks("hye", scholasticData, validateWithMarks))
                  .ye(calculateMarks("ye", scholasticData, validateWithMarks))
                  .term1totalMarks(getTotalTermPercentage(term1Total))
                  .term2totalMarks(getTotalTermPercentage(term2Total))
                  .graphTerm1TotalMarks(getTotalTermPercentage(term1Rounded))
                  .graphTerm2TotalMarks(getTotalTermPercentage(term2Rounded))
                  .term1total(
                      getTermTotal(
                          isCoScholasticSubject,
                          isOptional,
                          term1Total,
                          offlineTestDefinition.getGradeScaleSlug(),
                          termMaxMarks,
                          validateWithMarks))
                  .term2total(
                      getTermTotal(
                          isCoScholasticSubject,
                          isOptional,
                          term2Total,
                          offlineTestDefinition.getGradeScaleSlug(),
                          termMaxMarks,
                          validateWithMarks))
                  .overAllScored(overall)
                  .overAllExamMarks(totalMarks)
                  .subject(subject)
                  .grade1(grade1)
                  .grade2(grade2)
                  .pasTotal(
                      Boolean.TRUE.equals(validateWithMarks)
                          ? String.format("%.2f", paTotal)
                          : calculateGrade(
                              paTotal, paMaxMarks, offlineTestDefinition.getGradeScaleSlug()))
                  .hyeYe(
                      Boolean.TRUE.equals(validateWithMarks)
                          ? String.format("%.2f", hyeTotal)
                          : calculateGrade(
                              hyeTotal, hyeMaxMarks, offlineTestDefinition.getGradeScaleSlug()))
                  .overall(
                      Boolean.TRUE.equals(validateWithMarks)
                          ? String.format("%.2f", overall)
                          : calculateGrade(
                              overall, overAllMaxMarks, offlineTestDefinition.getGradeScaleSlug()))
                  .seqNo(scholasticData.get(0).getSeqNo())
                  .otdId(scholasticData.getFirst().getOtdId())
                  .term1grade("")
                  .term1description("")
                  .term2grade(term2grade)
                  .term2description(term2description)
                  .build());
        });

    return marksList;
  }

  public String getGradeContent(String subject, String gradeLevel, String grade) {
    Map<String, Object> gradesDescription = loadGradesDescription();

    Map<String, Object> subjectData = (Map<String, Object>) gradesDescription.get(subject);
    if (subjectData != null) {
      Map<String, String> gradeLevelData = (Map<String, String>) subjectData.get(gradeLevel);
      if (gradeLevelData != null) {
        String description = gradeLevelData.get(grade);
        if (description != null) {
          return description;
        }
      }
    }
    return null;
  }

  public Map<String, Object> loadGradesDescription() {
    try {
      var objectMapper = new ObjectMapper();
      return objectMapper.readValue(
          dpsGradesDescription.getInputStream(), new TypeReference<>() {});
    } catch (Exception e) {
      throw new RuntimeException("Failed to load grades description", e);
    }
  }

  private String getTermTotal(
      boolean isCoScholasticSubject,
      boolean isOptional,
      Double term1Total,
      String gradeScaleSlug,
      double totalMarks,
      Boolean withMarks) {
    if (isCoScholasticSubject) {
      return isOptional
          ? calculateCoScholasticGrade(term1Total, gradeScaleSlug)
          : calculateCoScholasticGrade(term1Total);
    }
    return Boolean.TRUE.equals(withMarks)
        ? (term1Total == 0.0 ? null : String.format("%.2f", term1Total))
        : calculateGrade(term1Total, totalMarks, gradeScaleSlug);
  }

  private String calculateMarks(
      String assessmentSlug, List<LowerGradeReportCardData> subjectData, Boolean withMarks) {
    return subjectData.stream()
        .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
        .findFirst()
        .map(
            data -> {
              if (data.getIsAttended() == null) {
                return null;
              } else if (Boolean.FALSE.equals(Boolean.valueOf(data.getIsAttended()))) {
                return Objects.isNull(data.getRemarks())
                    ? "AB"
                    : data.getRemarks().substring(0, 2).toUpperCase();
              }

              var offlineTestDefinition =
                  offlineTestScheduleService.validateOfflineTestDefinition(data.getOtdId());

              if (Boolean.TRUE.equals(isCoScholasticAndOptional(data))) {
                return data.getMarks() != null
                    ? getTermTotal(
                        true,
                        true,
                        data.getMarks(),
                        offlineTestDefinition.getGradeScaleSlug(),
                        data.getTotalMarks(),
                        withMarks)
                    : null;
              }
              if (data.getMarks() != null) {
                return Boolean.TRUE.equals(withMarks)
                    ? String.format("%.2f", data.getMarks())
                    : calculateGrade(
                        data.getMarks(),
                        data.getTotalMarks(),
                        offlineTestDefinition.getGradeScaleSlug());
              }
              return null;
            })
        .orElse(null);
  }

  private boolean isCoScholasticAndOptional(LowerGradeReportCardData data) {
    return SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(data.getCategory())
        && SubjectsTypeEnum.OPTIONAL.name().equals(data.getType());
  }

  private String calculateGrade(Double marks, Double totalMarks, String gradeScaleSlug) {
    return marks == null || marks == 0 || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate(
            gradeScaleSlug == null ? "4point" : gradeScaleSlug,
            BigDecimal.valueOf(
                Double.parseDouble(String.format("%.2f", (marks / totalMarks))) * 100));
  }

  private String calculateGrades(Double marks, Double totalMarks) {
    return marks == null
        ? null
        : pointScaleEvaluator.evaluate("4point", BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private String calculateCoScholasticGrade(Double marks) {
    return marks == null || marks == 0
        ? null
        : pointScaleEvaluator.evaluate("3point", BigDecimal.valueOf(marks));
  }

  private String calculateCoScholasticGrade(Double marks, String gradeScaleSlug) {
    return marks == null || marks == 0
        ? null
        : pointScaleEvaluator.evaluate(gradeScaleSlug, BigDecimal.valueOf(marks));
  }

  private Double getMarks(String assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    return subjectData.stream()
        .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
        .map(LowerGradeReportCardData::getMarks)
        .mapToDouble(Double::doubleValue)
        .sum();
  }

  private Double sumMarks(Double... marks) {
    return Arrays.stream(marks).filter(Objects::nonNull).mapToDouble(Double::doubleValue).sum();
  }

  private LowerGradeReportDto.TableMarks sortTable(
      List<Marks> firstTableMarks,
      List<Marks> externalMarks,
      List<Marks> secondTableMarks,
      List<Marks> thirdTableMarks) {
    List<Marks> firstTable = new ArrayList<>();
    List<LowerGradeReportDto.SecondTableMarks> secondTable = new ArrayList<>();
    List<Marks> externalTable = new ArrayList<>();
    List<LowerGradeReportDto.ThirdTableMarks> thirdTable = new ArrayList<>();
    var sortedFirstTable =
        firstTableMarks.stream().sorted(Comparator.comparingLong(Marks::seqNo)).toList();
    s: // learn.academyteacher.com
    for (int i = 0; i < sortedFirstTable.size(); i++) {
      Marks mark = sortedFirstTable.get(i);
      firstTable.add(
          Marks.builder()
              .sno(i + 1L)
              .pa1(mark.pa1())
              .pa2(mark.pa2())
              .hye(mark.hye())
              .pa3(mark.pa3())
              .pa4(mark.pa4())
              .ye(mark.ye())
              .termTotal(mark.termTotal())
              .term1total(mark.term1total())
              .term2total(mark.term2total())
              .term1totalMarks(mark.term1totalMarks())
              .term2totalMarks(mark.term2totalMarks())
              .graphTerm1TotalMarks(mark.graphTerm1TotalMarks())
              .graphTerm2TotalMarks(mark.graphTerm2TotalMarks())
              .pasTotal(mark.pasTotal())
              .hyeYe(mark.hyeYe())
              .subject(mark.subject())
              .overall(mark.overall())
              .overAllExamMarks(mark.overAllExamMarks())
              .overAllScored(mark.overAllScored())
              .grade2(mark.grade2())
              .grade1(mark.grade1())
              .otdId(mark.otdId())
              .build());
    }

    var sortedThirdTable =
        thirdTableMarks.stream().sorted(Comparator.comparingLong(Marks::seqNo)).toList();
    for (int i = 0; i < sortedThirdTable.size(); i++) {
      Marks mark = sortedThirdTable.get(i);
      thirdTable.add(
          LowerGradeReportDto.ThirdTableMarks.builder()
              .sno(sortedFirstTable.size() + i + 1L)
              .subject(mark.subject())
              .term1grade(mark.term1grade())
              .term1description(mark.term1description())
              .term2grade(mark.term2grade())
              .term2description(mark.term2description())
              .build());
    }

    var sortedExternalTable =
        externalMarks.stream().sorted(Comparator.comparingLong(Marks::seqNo)).toList();
    for (int i = 0; i < sortedExternalTable.size(); i++) {
      Marks mark = sortedExternalTable.get(i);
      externalTable.add(
          Marks.builder()
              .sno(sortedFirstTable.size() + sortedThirdTable.size() + i + 1L)
              .pa1(mark.pa1())
              .pa2(mark.pa2())
              .hye(mark.hye())
              .pa3(mark.pa3())
              .pa4(mark.pa4())
              .ye(mark.ye())
              .term1total(mark.term1total())
              .term2total(mark.term2total())
              .term1totalMarks(mark.term1totalMarks())
              .term2totalMarks(mark.term2totalMarks())
              .pasTotal(mark.pasTotal())
              .hyeYe(mark.hyeYe())
              .overAllScored(mark.overAllScored())
              .grade1(mark.grade1())
              .grade2(mark.grade2())
              .subject(mark.subject())
              .overall(mark.overall())
              .build());
    }
    var sortedSecondTable =
        secondTableMarks.stream().sorted(Comparator.comparingLong(Marks::seqNo)).toList();
    for (int i = 0; i < sortedSecondTable.size(); i++) {
      Marks mark = sortedSecondTable.get(i);
      var term1total = mark.term1total() == null ? mark.hye() : mark.term1total();
      var term2total = mark.term2total() == null ? mark.ye() : mark.term2total();
      secondTable.add(
          LowerGradeReportDto.SecondTableMarks.builder()
              .term1Grade(term1total)
              .term2Grade(term2total)
              .subjectName(mark.subject())
              .build());
    }
    return LowerGradeReportDto.TableMarks.builder()
        .firstTableMarks(firstTable)
        .externalMarks(externalTable)
        .secondTableMarks(secondTable)
        .thirdTableMarks(thirdTable)
        .build();
  }

  private double getTotalTermPercentage(Double marks) {
    return marks == null ? 0 : (marks / 50) * 100;
  }

  private LowerGradeReportDto.Totals buildTotals(List<Marks> firstTableMarks, Boolean withMarks) {
    var totalMarksScored =
        firstTableMarks.stream()
            .map(Marks::overAllScored)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();

    var totalMarks =
        firstTableMarks.stream()
            .map(Marks::overAllExamMarks)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();

    var offlineTestDef =
        offlineTestScheduleService.validateOfflineTestDefinition(
            firstTableMarks.getFirst().otdId());

    var total = (totalMarksScored / totalMarks) * 100;
    String grade =
        totalMarksScored == 0
            ? "N/A"
            : (Boolean.TRUE.equals(withMarks)
                ? String.format("%.2f", (totalMarksScored / totalMarks) * 100)
                : pointScaleEvaluator.evaluate(
                    offlineTestDef.getGradeScaleSlug() == null
                        ? "4point"
                        : offlineTestDef.getGradeScaleSlug(),
                    BigDecimal.valueOf((totalMarksScored / totalMarks) * 100)));

    return LowerGradeReportDto.Totals.builder().overallPercentage(grade).total(total).build();
  }

  public LowerGradeReportDto.Attendance buildAttendance(long studentId) {
    List<String> slugs = Arrays.asList("hye", "ye");
    var termAssessments = termAssessmentRepository.findAllBySlugIn(slugs);

    if (termAssessments.isEmpty()) {
      return LowerGradeReportDto.Attendance.builder().build();
    }

    var termAssessmentYe =
        termAssessments.stream()
            .filter(termAssessment -> "ye".equalsIgnoreCase(termAssessment.getSlug()))
            .findFirst();
    List<OfflineTestScheduleStudentAttendance> studentAttendancesTerm2 =
        offlineTestScheduleStudentAttendanceRepository.getStudentRemarks(
            studentId, termAssessmentYe.get().getId());

    if (studentAttendancesTerm2.isEmpty()) {
      return LowerGradeReportDto.Attendance.builder().build();
    }

    Optional<OfflineTestScheduleStudentAttendance> latestAttendance =
        studentAttendancesTerm2.stream()
            .filter(sa -> sa.getPresentDays() != null || sa.getRemarks() != null)
            .findFirst();

    if (latestAttendance.isEmpty()) {
      latestAttendance = Optional.of(studentAttendancesTerm2.getFirst());
    }

    Long totalAttendanceDays = getaLong(latestAttendance);
    Long daysPresent =
        studentAttendancesTerm2.stream()
            .map(OfflineTestScheduleStudentAttendance::getPresentDays)
            .filter(Objects::nonNull)
            .reduce(0L, Long::sum);

    Double attendancePercentage = null;
    double totalDays = totalAttendanceDays;
    if (totalDays > 0.0) {
      attendancePercentage = (double) Math.round(((double) daysPresent / totalDays) * 100);
    }
    String remarks =
        latestAttendance.get().getRemarks() != null ? latestAttendance.get().getRemarks() : " ";
    return LowerGradeReportDto.Attendance.builder()
        .workingDays(totalAttendanceDays)
        .daysPresent(daysPresent)
        .attendancePercentage(attendancePercentage)
        .remarks(remarks)
        .build();
  }

  private static Long getaLong(
      Optional<OfflineTestScheduleStudentAttendance> studentAttendancesTerm2) {

    var testDefinitionTerm2 =
        studentAttendancesTerm2
            .map(OfflineTestScheduleStudentAttendance::getOfflineTestDefinition)
            .orElse(null);

    Long attendanceDaysTerm2 =
        testDefinitionTerm2 != null && testDefinitionTerm2.getTotalAttendanceDays() != null
            ? Long.parseLong(testDefinitionTerm2.getTotalAttendanceDays())
            : 0L;

    return attendanceDaysTerm2;
  }

  public List<ReportCardConfigDto.GradeAndPercentage> getGradeAndPercentage(
      List<Student> students, Boolean withMarks) {
    List<ReportCardConfigDto.GradeAndPercentage> responseList = new ArrayList<>();
    students.forEach(
        student -> {
          try {
            var tableMarks = buildTableMarks(student, withMarks);
            var firstTable = tableMarks.firstTableMarks();
            var totals = buildTotals(firstTable, withMarks);
            responseList.add(
                ReportCardConfigDto.GradeAndPercentage.builder()
                    .grade(totals.overallPercentage())
                    .percentage(
                        totals.total().toString().equals("NaN") ? "-" : totals.total().toString())
                    .student(student)
                    .build());
          } catch (Exception ex) {
            responseList.add(
                ReportCardConfigDto.GradeAndPercentage.builder()
                    .grade("NA")
                    .percentage("-")
                    .student(student)
                    .build());
          }
        });
    return responseList;
  }

  public List<ReportCardConfigDto.GradeAndPercentageResponse> getAGradeResponse(
      List<Student> students, Boolean withMarks, String gradeSlug) {
    List<ReportCardConfigDto.GradeAndPercentageResponse> responseList = new ArrayList<>();
    students.forEach(
        student -> {
          try {
            var tableMarks = buildTableMarks(student, withMarks);
            var firstTable =
                buildFirstTable(
                    tableMarks.firstTableMarks(), tableMarks.externalMarks(), student, withMarks);
            boolean allSubjectsA1;
            Set<String> gradeSlugsForAPlus = Set.of("iii", "iv", "v");
            String overallPercentage = firstTable.totals().overallPercentage();

            if (gradeSlugsForAPlus.contains(gradeSlug)) {
              allSubjectsA1 = "A+".equals(overallPercentage);
            } else {
              allSubjectsA1 = "A".equals(overallPercentage);
            }

            if (allSubjectsA1) {
              var userInfo = student.getUserInfo();
              var name = userInfo.getFirstName() + " " + userInfo.getLastName();

              responseList.add(
                  ReportCardConfigDto.GradeAndPercentageResponse.builder()
                      .grade(firstTable.totals().overallPercentage())
                      .studentName(name)
                      .studentId(student.getId())
                      .sectionName(student.getSection().getName())
                      .build());
            }

          } catch (Exception ignored) {
          }
        });
    return responseList;
  }

  public List<ReportCardConfigDto.GradeAndPercentageResponse> getAGradeAllSubjectsSummary(
      List<Student> students, Boolean withMarks, String gradeSlug) {
    List<ReportCardConfigDto.GradeAndPercentageResponse> responseList = new ArrayList<>();
    students.forEach(
        student -> {
          try {
            var tableMarks = buildTableMarks(student, withMarks);
            var firstTable = tableMarks.firstTableMarks();
            Set<String> gradeSlugsForAPlus = Set.of("iii", "iv", "v");
            Set<String> uniqueGrades = null;
            if (gradeSlugsForAPlus.contains(gradeSlug)) {
              uniqueGrades =
                  firstTable.stream()
                      .map(Marks::overall)
                      .filter("A+"::equals)
                      .collect(Collectors.toSet());
            } else {
              uniqueGrades =
                  firstTable.stream()
                      .map(Marks::overall)
                      .filter("A"::equals)
                      .collect(Collectors.toSet());
            }

            var grade = uniqueGrades.size() == 1 ? uniqueGrades.iterator().next() : null;
            if (grade != null) {

              var userInfo = student.getUserInfo();
              var name = userInfo.getFirstName() + " " + userInfo.getLastName();
              responseList.add(
                  ReportCardConfigDto.GradeAndPercentageResponse.builder()
                      .grade(grade)
                      .studentName(name)
                      .studentId(student.getId())
                      .sectionName(student.getSection().getName())
                      .build());
            }

          } catch (Exception ignored) {
          }
        });
    return responseList;
  }

  public List<Map<String, Integer>> getGradeCounts(
      List<Student> students, Boolean withMarks, String gradeSlug, String subjectSlug) {

    Map<String, Integer> gradeCounts = new LinkedHashMap<>();
    int t1TotalStudents = 0;
    int t2TotalStudents = 0;

    for (Student student : students) {
      var termAssessments =
          termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
              Arrays.asList("t1", "t2"), student.getSection().getGradeSlug());

      if (termAssessments.isEmpty()) continue;

      var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();
      var data =
          reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
              student.getId(), termAssessmentIds);

      if (Objects.isNull(data) || data.isEmpty()) continue;

      var scholasticDataList =
          data.stream()
              .filter(
                  x ->
                      SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                          && SubjectsTypeEnum.MANDATORY.name().equals(x.getType())
                          && x.getSubjectSlug().equals(subjectSlug))
              .toList();

      var marksList = buildTableMarks(scholasticDataList, gradeSlug, withMarks);
      if (marksList.isEmpty()) continue;

      var marks = marksList.get(0);
      gradeCounts.merge(marks.grade1() + "(T1)", 1, Integer::sum);
      gradeCounts.merge(marks.grade2() + "(T2)", 1, Integer::sum);

      t1TotalStudents++;
      t2TotalStudents++;
    }

    gradeCounts.put("T1TOTALSTUDENTS", t1TotalStudents);
    gradeCounts.put("T2TOTALSTUDENTS", t2TotalStudents);

    Map<String, Integer> sortedMap = new TreeMap<>(gradeCounts);
    return List.of(sortedMap);
  }
}
