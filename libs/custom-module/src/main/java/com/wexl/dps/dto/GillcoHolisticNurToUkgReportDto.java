package com.wexl.dps.dto;

import com.wexl.gilico.dto.GilcoHolisticReportModeldto;
import java.util.List;
import lombok.Builder;

public class GillcoHolisticNurToUkgReportDto {

  @Builder
  public record Header(String imageUrl) {}

  @Builder
  public record Body(
      String name,
      String className,
      String sectionName,
      String rollNo,
      String admissionNumber,
      String house,
      String dateOfBirth,
      Integer age,
      String address,
      String fatherName,
      String motherName,
      GilcoHolisticReportModeldto.AllAboutMe allAboutMe,
      String term,
      Term1 term1,
      Term1 term2) {}

  @Builder
  public record Term1(
      CommunicativeDevelopment communicativeDevelopment,
      List<Development> cognitiveDevelopment,
      List<CategoryAndCriteria> physicalDevelopment,
      List<Development> socioEmotionalDevelopment,
      List<Development> learningSkills,
      String thematicConcepts,
      String remarks,
      String principalRemarks) {}

  @Builder
  public record Development(String subject, String skillValue) {}

  @Builder
  public record CommunicativeDevelopment(
      List<CategoryAndCriteria> english, List<CategoryAndCriteria> hindi) {}

  @Builder
  public record CategoryAndCriteria(String category, List<Criteria> criteria) {}

  @Builder
  public record Skills(String category, List<Criteria> criteria) {}

  @Builder
  public record Criteria(String skill, String value) {}
}
