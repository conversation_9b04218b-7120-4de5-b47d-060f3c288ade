package com.wexl.dps.reportcard;

import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.LowerGradeReportDto;
import com.wexl.retail.offlinetest.dto.PallaviProgressCardDto;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class PallaviTerm2ProgressCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final StudentAttributeService studentAttributeService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final PallaviProgressCard pallaviProgressCard;
  private final LowerGradeOverallReportCard lowerGradeOverallReportCard;
  private final OfflineTestScheduleService offlineTestScheduleService;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("pallavi-3rd-5th-grades-term2-progress-card.xml");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildPallaviHeader(user.getStudentInfo());
    var body = buildBody(user, request);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  private PallaviProgressCardDto.Header buildPallaviHeader(Student student) {
    return PallaviProgressCardDto.Header.builder()
        .schoolName(student.getSchoolName().toUpperCase())
        .academicSection(student.getAcademicYearSlug())
        .build();
  }

  private PallaviProgressCardDto.Body buildBody(User user, ReportCardDto.Request request) {
    var student = user.getStudentInfo();
    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    LowerGradeReportDto.Attendance attendance =
        lowerGradeOverallReportCard.buildAttendance(student.getId());
    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            List.of("t2"), student.getSection().getGradeSlug());
    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();
    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(request.offlineTestDefinitionId());
    var gradeScale =
        testDefinition.getGradeScaleSlug() != null ? testDefinition.getGradeScaleSlug() : "4point";
    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name())
                        && !x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    var coScholasticData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && !x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    var optionalData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    return PallaviProgressCardDto.Body.builder()
        .rollNumber(student.getClassRollNumber())
        .name(user.getFirstName() + " " + user.getLastName())
        .fatherName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .motherName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .orgSlug(user.getOrganization())
        .dateOfBirth(studentAttributeService.getDateOfBirthFormat(dateOfBirth))
        .classAndSection(
            student.getSection().getGradeSlug().toUpperCase()
                + " "
                + student.getSection().getName())
        .firstTable(buildFirstTable(scholasticDataList, gradeScale))
        .secondTable(pallaviProgressCard.buildSecondTable(coScholasticData, gradeScale))
        .thirdTable(buildFirstTable(optionalData, gradeScale))
        .attendance(pallaviProgressCard.buildAttendance(attendance))
        .promotedClass("")
        .date("")
        .place("")
        .gradingScale(gradeScale)
        .build();
  }

  public List<PallaviProgressCardDto.FirstTable> buildFirstTable(
      List<LowerGradeReportCardData> scholasticDataList, String gradeScale) {
    List<PallaviProgressCardDto.FirstTable> firstTables = new ArrayList<>();
    if (scholasticDataList.size() == 0) {
      return Collections.emptyList();
    }
    var scholasticDataMap =
        scholasticDataList.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));
    AtomicLong snoCounter = new AtomicLong(1);
    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var pa3 = pallaviProgressCard.getPaMarks("pa3", scholasticData);
          var pa4 = pallaviProgressCard.getPaMarks("pa4", scholasticData);
          var fe = pallaviProgressCard.getMarks("ye", scholasticData);

          var term2Total = pallaviProgressCard.sumMarks(pa3, pa4, String.valueOf(fe));
          Double termsTotalMarks = 100.0;

          String term2Grade =
              pallaviProgressCard.calculateGrades(
                  Double.parseDouble(term2Total), termsTotalMarks, gradeScale);

          firstTables.add(
              PallaviProgressCardDto.FirstTable.builder()
                  .sno(snoCounter.getAndIncrement())
                  .subjectName(subject)
                  .pa3(pa3)
                  .pa4(pa4)
                  .finalMarks(fe)
                  .term2Marks(term2Total)
                  .term2Grade(term2Grade)
                  .build());
        });
    return firstTables;
  }
}
