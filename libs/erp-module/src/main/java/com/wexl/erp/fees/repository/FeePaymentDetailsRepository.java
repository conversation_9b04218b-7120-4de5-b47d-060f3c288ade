package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.model.FeePaymentDetail;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FeePaymentDetailsRepository extends JpaRepository<FeePaymentDetail, Long> {

  List<FeePaymentDetail> findByFeeHead(FeeHead feeHead);
}
