package com.wexl.erp.receipt.controller;

import com.wexl.erp.receipt.service.FeeReceiptService;
import com.wexl.retail.commons.security.annotation.IsStaff;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/staff/{staffAuthUserId}/fee-receipts")
public class ReceiptController {

  private final FeeReceiptService feeReceiptService;

  @IsStaff
  @PostMapping(
      value = "/{studentAuthId}/fee-head/{feeHeadId}/receipt",
      produces = MediaType.APPLICATION_PDF_VALUE)
  public byte[] generateReceipt(
      @PathVariable String orgSlug,
      @PathVariable String studentAuthId,
      @PathVariable String feeHeadId) {
    return feeReceiptService.generateReceipt(orgSlug, studentAuthId, feeHeadId);
  }
}
