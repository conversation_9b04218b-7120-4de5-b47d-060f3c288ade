package com.wexl.erp.paymentGateway.controller;

import com.wexl.erp.fees.service.FeeCollectionService;
import com.wexl.erp.paymentGateway.dto.PaymentGatewayDto;
import com.wexl.erp.paymentGateway.dto.PaymentStatus;
import com.wexl.erp.paymentGateway.types.EaseBuzz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("public/orgs/{orgSlug}")
public class PaymentCallbackController {

  private final EaseBuzz easeBuzzService;
  private final FeeCollectionService feeCollectionService;

  @PostMapping("/easebuzz-payment/success")
  public ResponseEntity<Void> paymentSuccess(
      @PathVariable String orgSlug, @RequestBody PaymentGatewayDto.CallBackRequest params) {
    log.info("Easebuzz Success Callback: {}", params);

    if (!easeBuzzService.verifyHash(orgSlug, params)) {
      log.error("Hash mismatch in success callback! Possible tampering.");
      return ResponseEntity.badRequest().build();
    }

    String txnId = params.txnId();
    feeCollectionService.updateTransactionStatus(txnId, PaymentStatus.SUCCESS);
    return ResponseEntity.status(HttpStatus.ACCEPTED).build();
  }

  @PostMapping("/easebuzz-payment/failure")
  public ResponseEntity<Void> paymentFailure(
      @PathVariable String orgSlug, @RequestBody PaymentGatewayDto.CallBackRequest params) {
    log.warn("Easebuzz Failure Callback: {}", params);

    if (!easeBuzzService.verifyHash(orgSlug, params)) {
      log.error("Hash mismatch in failure callback! Possible tampering.");
      return ResponseEntity.badRequest().build();
    }

    String txnId = params.txnId();
    feeCollectionService.updateTransactionStatus(txnId, PaymentStatus.FAILED);
    return ResponseEntity.status(HttpStatus.ACCEPTED).build();
  }
}
