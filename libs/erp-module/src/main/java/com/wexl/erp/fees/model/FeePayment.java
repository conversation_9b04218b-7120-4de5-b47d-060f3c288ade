package com.wexl.erp.fees.model;

import com.wexl.erp.paymentGateway.dto.PaymentMethod;
import com.wexl.erp.paymentGateway.dto.PaymentStatus;
import com.wexl.erp.paymentGateway.model.PaymentGatewayDetail;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import jakarta.persistence.*;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "fee_payments")
public class FeePayment extends Model {
  @Id @GeneratedValue private UUID id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "student_id")
  private Student student;

  @Column(name = "total_amount_paid", nullable = false)
  private Double totalAmountPaid;

  @Enumerated(EnumType.ORDINAL)
  @Column(name = "payment_method", nullable = false)
  private PaymentMethod paymentMethod;

  @ManyToOne
  @JoinColumn(name = "payment_gateway_detail_id")
  private PaymentGatewayDetail paymentGatewayDetail;

  @Column(name = "reference_id")
  private String referenceId;

  @Column(name = "payment_status")
  private PaymentStatus paymentStatus;

  @Column(name = "razor_pay_transaction_id")
  private String razorPayTransactionId;

  @Column(name = "razor_pay_payment_id")
  private String razorpayPaymentId;

  private String orgSlug;

  private String txnId;
}
