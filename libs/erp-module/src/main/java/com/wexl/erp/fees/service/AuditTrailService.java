package com.wexl.erp.fees.service;

import com.wexl.erp.fees.model.FeeHeadAuditTrail;
import com.wexl.erp.fees.repository.AuditTrailRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
public class AuditTrailService {
  private final AuditTrailRepository auditTrailRepository;

  public void log(
      String entityId,
      String entityName,
      String reason,
      String oldStatus,
      String newStatus,
      String orgSlug,
      Long updatedBy) {

    FeeHeadAuditTrail audit =
        FeeHeadAuditTrail.builder()
            .entityId(entityId)
            .entityName(entityName)
            .reason(reason)
            .orgSlug(orgSlug)
            .oldValue(oldStatus)
            .newValue(newStatus)
            .updatedBy(updatedBy)
            .build();

    auditTrailRepository.save(audit);
  }
}
