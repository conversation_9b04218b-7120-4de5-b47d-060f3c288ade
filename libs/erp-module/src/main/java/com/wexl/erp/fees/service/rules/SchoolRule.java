package com.wexl.erp.fees.service.rules;

import com.wexl.erp.fees.service.FeeService;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.dto.response.SectionEntityDto;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.util.StrapiService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SchoolRule implements Rule {

  private final StudentRepository studentRepository;
  private final CurriculumService curriculumService;
  private final FeeService feeService;
  private final StrapiService strapiService;
  private final SectionService sectionService;

  @Override
  public boolean isApplicable(RuleDto.StudentFeeDto student, RuleDto.RuleParam ruleParam) {
    // Check if student.orgSlug() is in ruleParam.paramValues(), check ignore case
    return ruleParam.paramValues().stream()
        .anyMatch(value -> value.equalsIgnoreCase(student.orgSlug()));
  }

  @Override
  public boolean supports(RuleDto.RuleParam ruleParam) {
    return ruleParam.paramType().equals(RuleParamType.SCHOOL);
  }

  @Override
  public List<Student> getStudents(RuleDto.RuleParam ruleParam) {
    var feeMaster =
        feeService.getFeeMasterById(String.valueOf(ruleParam.feeMasterId()), ruleParam.orgSlug());
    var board = strapiService.getEduBoardBySlug(feeMaster.getBoardSlug());
    final var grades = curriculumService.getGradesByBoardId(ruleParam.orgSlug(), board.getId());

    var sections =
        grades.stream()
            .flatMap(
                grade ->
                    sectionService
                        .getSectionsByGrade(ruleParam.orgSlug(), grade.getSlug())
                        .stream())
            .map(SectionEntityDto.Response::uuid)
            .distinct()
            .toList();

    return studentRepository.getStudentsBySectionUuidsAndOrgSlug(sections, ruleParam.orgSlug());
  }
}
