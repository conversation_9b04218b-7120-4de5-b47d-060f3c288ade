package com.wexl.erp.fees.controller;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.service.FeeConcessionService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/fee-concessions")
public class FeeConcessionController {

  private final FeeConcessionService feeConcessionService;

  @PostMapping()
  public void saveConcessions(
      @RequestBody FeeDto.ConcessionRequest request, @PathVariable String orgSlug) {
    feeConcessionService.saveConcessions(orgSlug, request);
  }

  @GetMapping()
  public List<FeeDto.ConcessionResponse> getConcessions(@PathVariable String orgSlug) {
    return feeConcessionService.getConcessions(orgSlug);
  }

  @PutMapping("/{concessionId}")
  public void updateConcessionById(
      @PathVariable String orgSlug,
      @PathVariable String concessionId,
      @RequestBody FeeDto.ConcessionRequest request) {
    feeConcessionService.updateConcessionById(orgSlug, concessionId, request);
  }

  @DeleteMapping("/{concessionId}")
  public void deleteConcessionById(
      @PathVariable String orgSlug, @PathVariable String concessionId) {
    feeConcessionService.deleteConcessionById(orgSlug, concessionId);
  }

  @PostMapping("/{concessionId}/publish")
  public void publishConcession(@PathVariable String orgSlug, @PathVariable String concessionId) {
    feeConcessionService.publishConcession(orgSlug, concessionId);
  }

  @PostMapping("/concession-heads")
  public void saveConcessionHeads(
      @RequestBody FeeDto.ConcessionHeadRequest request, @PathVariable String orgSlug) {
    feeConcessionService.saveConcessionHeads(orgSlug, request);
  }

  @GetMapping("/{concessionId}/concession-heads")
  public List<FeeDto.ConcessionHeadResponse> getConcessionHeads(
      @PathVariable String orgSlug, @PathVariable String concessionId) {
    return feeConcessionService.getConcessionHeads(orgSlug, concessionId);
  }

  @DeleteMapping("/{concessionId}/concession-heads/{concessionHeadId}")
  public void deleteConcessionHeadById(
      @PathVariable String orgSlug,
      @PathVariable String concessionId,
      @PathVariable String concessionHeadId) {
    feeConcessionService.deleteConcessionHeadById(orgSlug, concessionId, concessionHeadId);
  }

  @PostMapping("/{concessionId}/assign")
  public void assignConcessions(
      @RequestBody FeeDto.AssignConcessionRequest request,
      @PathVariable String concessionId,
      @PathVariable String orgSlug) {
    feeConcessionService.assignConcessions(orgSlug, request, concessionId);
  }
}
