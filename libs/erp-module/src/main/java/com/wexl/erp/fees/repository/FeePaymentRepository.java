package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.model.FeePayment;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FeePaymentRepository extends JpaRepository<FeePayment, Long> {
  Optional<FeePayment> findByIdAndOrgSlug(UUID uuid, String orgSlug);

  Optional<FeePayment> findByTxnId(String txnId);
}
