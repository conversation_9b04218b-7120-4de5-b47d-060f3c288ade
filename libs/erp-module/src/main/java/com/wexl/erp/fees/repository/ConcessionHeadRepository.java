package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.model.Concession;
import com.wexl.erp.fees.model.ConcessionHead;
import com.wexl.erp.fees.model.FeeType;
import com.wexl.retail.model.Student;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ConcessionHeadRepository extends JpaRepository<ConcessionHead, Long> {
  List<ConcessionHead> findAllByConcession(Concession concession);

  Optional<ConcessionHead> findById(UUID uuid);

  Optional<ConcessionHead> findByConcessionAndStudentAndFeeType(
      Concession concession, Student student, FeeType feeType);
}
