<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Questions</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap');
        body {
            margin: 0;
            font-family: 'Poppins', sans-serif;
            font-size: 14px;
            background: #fff;
            color: #333;
        }
        .container {
            width: 90%;
            margin: 0 auto;
            padding: 20px;
        }
        .question-block {
            border: 1px solid #f6b519;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            background: #fefae0;
        }
        .question-header {
            font-weight: 600;
            margin-bottom: 10px;
        }
        .options {
            margin-left: 15px;
        }
        .option {
            margin-bottom: 5px;
        }
        .answer {
            font-weight: bold;
            margin-top: 10px;
            color: green;
        }
        .explanation {
            margin-top: 5px;
            font-style: italic;
            color: #555;
        }
        .marks {
            float: right;
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="container">
    <h2 style="text-align:center;">Question Paper</h2>

    <!-- Start Question Loop -->
    <div class="question-block" th:each="question, iterStat : ${questions}">
        <div class="question-header">
            <span th:text="${iterStat.index + 1} + ') '"></span>
            <span th:text="${question.question}"></span>
            <span class="marks" th:text="${question.marks} + ' M.'"></span>
        </div>

        <div class="options" th:if="${question.mcq != null}">
            <div class="option">a) <span th:text="${question.mcq.option1}"></span></div>
            <div class="option">b) <span th:text="${question.mcq.option2}"></span></div>
            <div class="option">c) <span th:text="${question.mcq.option3}"></span></div>
            <div class="option">d) <span th:text="${question.mcq.option4}"></span></div>
        </div>

    </div>
    <!-- End Question Loop -->

</div>
</body>
</html>