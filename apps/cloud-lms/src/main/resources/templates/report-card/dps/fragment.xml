
<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="8point">

<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="8point">
    <fo:block font-size="10" font-weight="bold" font-family="Times New Roman, serif">Grade Scale
    </fo:block>

    <fo:table border="1pt solid black">
        <fo:table-column column-width="21.5mm"/>
        <fo:table-column column-width="21.5mm"/>
        <fo:table-column column-width="21.5mm"/>
        <fo:table-column column-width="21.5mm"/>
        <fo:table-column column-width="21.5mm"/>
        <fo:table-column column-width="21.5mm"/>
        <fo:table-column column-width="21.5mm"/>
        <fo:table-column column-width="21.5mm"/>
        <fo:table-header>
            <fo:table-row>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>91-100</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>81-90</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>71-80</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>61-70</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>51-60</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>41-50</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>33-40</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>32 &amp; Below </fo:block>
                </fo:table-cell>
            </fo:table-row>
        </fo:table-header>
        <fo:table-body>
            <fo:table-row>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                    <fo:block>A1</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                    <fo:block>A2</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                    <fo:block>B1</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                    <fo:block>B2</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                    <fo:block>C1</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                    <fo:block>C2</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                    <fo:block>D</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black"  padding="1mm"
                               text-align="center">
                    <fo:block>E - NI</fo:block>
                </fo:table-cell>
            </fo:table-row>
        </fo:table-body>
    </fo:table>
</fo:block>
</fo:block-container>



<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="9point">

<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="9point">
    <fo:block font-size="10" font-weight="bold" font-family="Times New Roman, serif">Grade Scale
    </fo:block>

    <fo:table border="1pt solid black">
        <fo:table-column column-width="19.1mm"/>
        <fo:table-column column-width="19.1mm"/>
        <fo:table-column column-width="19.1mm"/>
        <fo:table-column column-width="19.1mm"/>
        <fo:table-column column-width="19.1mm"/>
        <fo:table-column column-width="19.1mm"/>
        <fo:table-column column-width="19.1mm"/>
        <fo:table-column column-width="19.1mm"/>
        <fo:table-column column-width="19.1mm"/>
        <fo:table-header>
            <fo:table-row>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>90-100</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>80-89</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>70-79</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>60-69</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>50-59</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>40-49</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>30-39</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>20-29</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>0-19</fo:block>
                </fo:table-cell>
            </fo:table-row>
        </fo:table-header>
        <fo:table-body>
            <fo:table-row>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                    <fo:block>A*</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                    <fo:block>A</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                    <fo:block>B</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                    <fo:block>C</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                    <fo:block>D</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                    <fo:block>E</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                    <fo:block>F</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                    <fo:block>G</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                               text-align="center">
                    <fo:block>U(Failed)</fo:block>
                </fo:table-cell>
            </fo:table-row>
        </fo:table-body>
    </fo:table>
</fo:block>
</fo:block-container>




<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="6point">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="6point">
    <fo:block border-width="1mm" font-size="10pt" space-before="1mm" font-family="Times New Roman, serif" >
        <fo:block   font-size="10" font-weight="bold" font-family="Times New Roman, serif">Grade Scale</fo:block>

        <fo:table border="1pt solid black">
            <fo:table-column column-width="28.6mm" />
            <fo:table-column column-width="28.6mm" />
            <fo:table-column column-width="28.6mm" />
            <fo:table-column column-width="28.6mm" />
            <fo:table-column column-width="28.7mm" />
            <fo:table-column column-width="28.7mm" />

            <fo:table-header>
                <fo:table-row>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>91-100</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>81-90</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>71-80</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>55-70</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>40-54</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>0-39</fo:block>
                    </fo:table-cell>

                </fo:table-row>
            </fo:table-header>
            <fo:table-body>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >A+</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >A</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >B+</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >B</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >C</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >D</fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </fo:table-body>
        </fo:table>
    </fo:block>
</fo:block>
</fo:block-container>



<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="6point_pre_primary">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="6point_pre_primary">
    <fo:block border-width="1mm" font-size="10pt" space-before="1mm" font-family="Times New Roman, serif" >
        <fo:block   font-size="10" font-weight="bold" font-family="Times New Roman, serif">Grade Scale</fo:block>

        <fo:table border="1pt solid black">
            <fo:table-column column-width="28.6mm" />
            <fo:table-column column-width="28.6mm" />
            <fo:table-column column-width="28.6mm" />
            <fo:table-column column-width="28.6mm" />
            <fo:table-column column-width="28.7mm" />
            <fo:table-column column-width="28.7mm" />
            <fo:table-header>
                <fo:table-row>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>45-100</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>38-44</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>28-37</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>18-27</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>0-17</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>-</fo:block>
                    </fo:table-cell>

                </fo:table-row>
            </fo:table-header>
            <fo:table-body>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >O</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >E</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >A</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >D</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >B</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center" >
                        <fo:block>NA</fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </fo:table-body>
        </fo:table>
    </fo:block>
</fo:block>
</fo:block-container>


<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="5point">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="5point">
    <fo:block border-width="1mm" font-size="10pt" space-before="1mm" font-family="Times New Roman, serif" >
        <fo:block   font-size="10" font-weight="bold" font-family="Times New Roman, serif">Grade Scale</fo:block>

        <fo:table border="1pt solid black">
            <fo:table-column column-width="35.2mm" />
            <fo:table-column column-width="35.2mm" />
            <fo:table-column column-width="35.2mm" />
            <fo:table-column column-width="35.2mm" />
            <fo:table-column column-width="35.2mm" />

            <fo:table-header>
                <fo:table-row>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>91-100</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>75-90</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>60-74</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>40-59</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>0-39</fo:block>
                    </fo:table-cell>

                </fo:table-row>
            </fo:table-header>
            <fo:table-body>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >A+</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >A</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >B</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >C</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >D</fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </fo:table-body>
        </fo:table>
    </fo:block>
</fo:block>
</fo:block-container>
<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="5point-scholastic">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="5point-scholastic">
    <fo:block border-width="1mm" font-size="10pt" space-before="1mm" font-family="Times New Roman, serif" >
        <fo:block   font-size="10" font-weight="bold" font-family="Times New Roman, serif">Grade Scale</fo:block>

        <fo:table border="1pt solid black">
            <fo:table-column column-width="35.5mm" />
            <fo:table-column column-width="35.5mm" />
            <fo:table-column column-width="35.5mm" />
            <fo:table-column column-width="35.5mm" />
            <fo:table-column column-width="35.5mm" />

            <fo:table-header>
                <fo:table-row>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>91-100</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>75-90</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>60-74</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>40-59</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>0-39</fo:block>
                    </fo:table-cell>

                </fo:table-row>
            </fo:table-header>
            <fo:table-body>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >A</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >B</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >C</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >D</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >E</fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </fo:table-body>
        </fo:table>
    </fo:block>
</fo:block>
</fo:block-container>

<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="4point">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="4point">
    <fo:block border-width="1mm" font-size="10pt" space-before="1mm" font-family="Times New Roman, serif" >
        <fo:block   font-size="10" font-weight="bold" font-family="Times New Roman, serif">Grade Scale</fo:block>

        <fo:table border="1pt solid black">
            <fo:table-column column-width="43.125mm" />
            <fo:table-column column-width="43.125mm" />
            <fo:table-column column-width="43.125mm" />
            <fo:table-column column-width="43.125mm" />
            <fo:table-header>
                <fo:table-row>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>81-100</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>61-80</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>41-60</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>0-40</fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </fo:table-header>
            <fo:table-body>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >A</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >B</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >C</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >D</fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </fo:table-body>
        </fo:table>
    </fo:block>
</fo:block>
</fo:block-container>
        <!--dynamic english-->
<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="mathmatics-i">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="mathmatics-i">
    <fo:block border-width="1mm" font-size="10pt" space-before="1mm" font-family="Times New Roman, serif" >
        <fo:block   font-size="10" font-weight="bold" font-family="Times New Roman, serif">Grade Scale</fo:block>

        <fo:table border="1pt solid black">
            <fo:table-column column-width="10mm" />
            <fo:table-column column-width="50mm" />
            <fo:table-column column-width="119.5mm" />
            <fo:table-header>
                <fo:table-row>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>S.NO</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>GRADE MARK LEGEND</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>DESCRIPTION </fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </fo:table-header>
            <fo:table-body>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >1</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >A</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Able to do picture-based single-digit subtraction without regrouping</fo:block>
                    </fo:table-cell>
                </fo:table-row>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >2</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >B</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Able to do picture-based single-digit addition without regrouping</fo:block>
                    </fo:table-cell>
                </fo:table-row>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >3</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >C</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Able to read two-digit numbers</fo:block>
                    </fo:table-cell>
                </fo:table-row>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >4</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >D</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Able to read single-digit numbers</fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </fo:table-body>
        </fo:table>
    </fo:block>
</fo:block>
</fo:block-container>

<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="mathmatics-ii">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="mathmatics-ii">
    <fo:block border-width="1mm" font-size="10pt" space-before="1mm" font-family="Times New Roman, serif" >
        <fo:block   font-size="10" font-weight="bold" font-family="Times New Roman, serif">Grade Scale</fo:block>

        <fo:table border="1pt solid black">
            <fo:table-column column-width="10mm" />
            <fo:table-column column-width="50mm" />
            <fo:table-column column-width="119.5mm" />
            <fo:table-header>
                <fo:table-row>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>S.NO</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>GRADE MARK LEGEND</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>DESCRIPTION </fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </fo:table-header>
            <fo:table-body>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >1</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >A</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Able to do two-digit subtraction without regrouping</fo:block>
                    </fo:table-cell>
                </fo:table-row>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >2</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >B</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Able to do two-digit addition without regrouping</fo:block>
                    </fo:table-cell>
                </fo:table-row>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >3</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >C</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Able to read two-digit numbers</fo:block>
                    </fo:table-cell>
                </fo:table-row>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >4</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >D</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Able to read single-digit numbers</fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </fo:table-body>
        </fo:table>
    </fo:block>
</fo:block>
</fo:block-container>

<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="english-ii">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="english-ii">
    <fo:block border-width="1mm" font-size="10pt" space-before="1mm" font-family="Times New Roman, serif" >
        <fo:block   font-size="10" font-weight="bold" font-family="Times New Roman, serif">Grade Scale</fo:block>

        <fo:table border="1pt solid black">
            <fo:table-column column-width="10mm" />
            <fo:table-column column-width="50mm" />
            <fo:table-column column-width="119.5mm" />
            <fo:table-header>
                <fo:table-row>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>S.NO</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>GRADE MARK LEGEND</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>DESCRIPTION </fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </fo:table-header>
            <fo:table-body>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >1</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >A</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Reads longer sentences with more nuance, more than 50 words,Reads simple sentences with 4 – 5 letter words, word repetition, more than 50 words</fo:block>
                    </fo:table-cell>
                </fo:table-row>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >2</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >B</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Reads familiar words, word repetition, less than 25 words,Reads fluently and with ease even if it is slow, pronounces the words correctly and easily and does not stop between words </fo:block>
                    </fo:table-cell>
                </fo:table-row>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >3</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >C</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Reads at least four out of five 3 – 4 letter words correctly</fo:block>
                    </fo:table-cell>
                </fo:table-row>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >4</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >D</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Able to recognize at least four out of five letters correctly</fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </fo:table-body>
        </fo:table>
    </fo:block>
</fo:block>
</fo:block-container>

<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="english-i">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="english-i">
    <fo:block border-width="1mm" font-size="10pt" space-before="1mm" font-family="Times New Roman, serif" >
        <fo:block   font-size="10" font-weight="bold" font-family="Times New Roman, serif">Grade Scale</fo:block>

        <fo:table border="1pt solid black">
            <fo:table-column column-width="10mm" />
            <fo:table-column column-width="50mm" />
            <fo:table-column column-width="119.5mm" />
            <fo:table-header>
                <fo:table-row>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>S.NO</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                        <fo:block>GRADE MARK LEGEND</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                        <fo:block>DESCRIPTION </fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </fo:table-header>
            <fo:table-body>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >1</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >A</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Reads longer sentences with more nuance, more than 25 words,Reads simple sentences with 3 – 4 letter words, word repetition, more than 25 words</fo:block>
                    </fo:table-cell>
                </fo:table-row>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >2</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >B</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Reads familiar words, word repetition, less than 15 words,Reads fluently and with ease even if it is slow, pronounces the words correctly and easily and does not stop between words </fo:block>
                    </fo:table-cell>
                </fo:table-row>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >3</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >C</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Reads at least four out of five 2 – 3 letter words correctly</fo:block>
                    </fo:table-cell>
                </fo:table-row>
                <fo:table-row >
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >4</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >D</fo:block>
                    </fo:table-cell>
                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                        <fo:block >Able to recognize at least four out of five letters correctly</fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </fo:table-body>
        </fo:table>
    </fo:block>
</fo:block>
</fo:block-container>

<!--dps address -->
<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="dps688668">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="dps688668">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
        Plot No.44, 42A, BEHIND NACHARAM TELEPHONE EXCHANGE, NACHARAM,UPPAL(M),MEDCHAL DISTRICT,HYDERABAD-500076
    </fo:block>
</fo:block>
</fo:block-container>

<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="doo784335">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="doo784335">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
        Durga enclave, Saraswati Colony, Sehatpur, Faridabad, Haryana 121003
    </fo:block>
</fo:block>
</fo:block-container>

<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="gil923272">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="gil923272">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
        Gillco Valley, Sector -127, Mohali
    </fo:block>
</fo:block>
</fo:block-container>


<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="pro162316">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="pro162316">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
        Level 1, Hub Town Prime, 3/2, Annaswamy Mudaliar Rd, Halasuru, Rukmani Colony, Sivanchetti Gardens, Bengaluru, Karnataka 560042
    </fo:block>
</fo:block>
</fo:block-container>


<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="apj858074">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="apj858074">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
        Level 1, Hub Town Prime, 3/2, Annaswamy Mudaliar Rd, Halasuru, Rukmani Colony, Sivanchetti Gardens, Bengaluru, Karnataka 560042
    </fo:block>
</fo:block>
</fo:block-container>

<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="pal332908">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="pal332908" padding-top="-2mm">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
        6JC5+M8W, Hyderabad-Nagarjuna Sagar, Highway, Chintapally Guda, Ibrahimpatnam, Telangana 501510
    </fo:block>
</fo:block>
</fo:block-container>

<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="del189476">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="del189476">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
        Survey No 191/1, 191/2, 191/3&amp;192, Rajiv Gandhi International Airport Road, Mamidipally, Shamshabad, Rangareddy - 501218
    </fo:block>
</fo:block>
</fo:block-container>
<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="del909850">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="del909850">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
        Survey No 74/9, Behind Tukaram Gate Police Station, Mahendra Hills, East Marredpally Hyderabad, Telangana 500026
    </fo:block>
</fo:block>
</fo:block-container>
<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="del765517">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="del765517">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
        DPS NADERGUL, SURVEY NO.469/1,NADERGUL VILLAGE, RANGA REDDY DISTRICT, RANGA REDDY, - 501510
    </fo:block>
</fo:block>
</fo:block-container>
<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="del217242">
<fo:block border-width="1mm" font-size="10pt" space-before="1mm"
          font-family="Times New Roman, serif" th:fragment="del217242" >
    <fo:block font-size="10pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center" space-after="1mm" >
        FEEDER SCHOOL OF NADERGUL
    </fo:block>
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center" >
        Survey No. 180, Indraprastha Colony, Road No. 2, Near Hanuman Temple, Saidabad, Hyderabad, Telangana - 500059
    </fo:block>
</fo:block>
</fo:block-container>
<!--pallavi address-->
<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="pal174599">
<fo:block border-width="1mm" font-size="10pt" space-before="8mm"
          font-family="Times New Roman, serif" th:fragment="pal174599" padding-top="-3mm">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
    Beside Aware Global Hospitals, Bairamalguda, Saroornagar, Hyderabad,  Telangana, India. Pin: 500035.
    </fo:block>
</fo:block>
</fo:block-container>
<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="pal454783">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="pal454783">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
        Pallavi School, 786, Gandipet X Road, Moinabad Mandal, Hyderabad, Telangana - 500075
    </fo:block>
</fo:block>
</fo:block-container>
<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="pal988947">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="pal988947">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
        PLOT NO:-1-5-563/1/414D/NR. NEAR PAKALAKUNTA,ALWAL,SEC-500010
    </fo:block>
</fo:block>
</fo:block-container>
<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="pal233196">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="pal233196">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
        HASMATPET MAIN ROAD, MANOVIKAS NAGAR, SEC – 500009
    </fo:block>
</fo:block>
</fo:block-container>
<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="pal556078">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="pal556078">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
        Tirumalagiri -Lalbazar Bsnl office,S. No. 16, 17,500015
    </fo:block>
</fo:block>
</fo:block-container>
<!-- sai school address -->
<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="sai681502">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="sai681502">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
        Village Ladora Narayanpur,Uttar Pradesh PIN: 244901
    </fo:block>
</fo:block>
</fo:block-container>
<!--nalanda school address -->
<fo:block-container width="100%" height="100%" margin-top="0cm"  th:fragment="nal635825">
<fo:block border-width="1mm" font-size="10pt" space-before="5mm"
          font-family="Times New Roman, serif" th:fragment="nal635825">
    <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
        Plot No.44, 42A, BEHIND NACHARAM TELEPHONE EXCHANGE, NACHARAM,UPPAL(M),MEDCHAL DISTRICT,HYDERABAD-500076
    </fo:block>
</fo:block>
</fo:block-container>

<!--dps school participation certificate address-->
<th:block th:fragment="dps688668Participation (orgSlug, participationcertificate)">
<fo:block-container width="100%" height="100%" margin-top="0cm"
                    th:if="${(orgSlug == 'dps688668' || orgSlug == 'del909850' || orgSlug == 'del189476' || orgSlug == 'del765517' || orgSlug == 'del217242')
                     and participationcertificate == 'ParticipationCertificate'}">
    <fo:block border-width="1mm" font-size="8pt" space-before="8mm"
              font-family="PT Serif">
        <fo:block font-size="8pt" color="#666666" font-family="Arial, sans-serif" text-align="center">
            NACHARAM, MAHENDRA HILLS, AEROCITY, NADERGUL, SANTOSH NAGAR (Feeder School of Nadergul)
        </fo:block>
    </fo:block>
</fo:block-container>
</th:block>

<!-- pallavi school participation certificate address -->
<th:block th:fragment="dps688668Participation (orgSlug, participationcertificate)">
<fo:block-container width="100%" height="100%" margin-top="0cm"
                    th:if="${(orgSlug == 'pal988947' || orgSlug == 'pal454783' || orgSlug == 'pal556078' || orgSlug == 'pal174599' || orgSlug == 'pal332908' || orgSlug == 'pal233196')
                     and participationcertificate == 'ParticipationCertificate'}">
    <fo:block border-width="1mm" font-size="13pt" space-before="8mm"
              font-family="Times New Roman, serif">
        <fo:block font-size="8pt" color="#666666" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
            ALWAL, GANDIPET, TIRUMALAGIRI, SAROOR NAGAR, SAGAR ROAD,
        </fo:block>
        <fo:block font-size="8pt" color="#666666" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
             BOWENPALLY
        </fo:block>
    </fo:block>
</fo:block-container>
</th:block>
