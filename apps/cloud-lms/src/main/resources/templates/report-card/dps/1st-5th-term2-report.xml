<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="15mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="0cm" border="2pt solid black" padding="6mm">
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="19"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="19"/>
                                            <feFuncB type="linear" slope="19"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter)" x="0" y="0" width="100%" height="100%" xlink:href="https://dpsgurugram84.com/wp-content/uploads/2019/07/logo_dps.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none">
                    <fo:table-column column-width="2mm" />
                    <fo:table-column column-width="180mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block >
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg")'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" space-after="3pt" text-align="center">
                                    DELHI PUBLIC SCHOOL
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt">
                                    PLOT NO:44,42A,BEHIND NACHARAM TELEPHONE EXCHANGE,NACHARAM,UPPAL(M),MEDCHAL
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt">
                                    Affiliated to CBSE, New Delhi,Affiliation No :3630057
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="0pt">
                                    ISO:9001-2005,ISO:45001-2018,ISO:21001-2018
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt">
                                    Record
                                    of
                                    Academic Performance
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="6pt">
                                    Session:2023-24
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif"  space-after="2pt">
                    <fo:table border="none">
                        <fo:table-column column-width="33mm" />
                        <fo:table-column column-width="85mm" />
                        <fo:table-column column-width="26mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Student Id   :</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.header.studentId}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Class &amp; Section :</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.className}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Name of the Student :</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.name}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Roll No   :</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.rollNumber}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Mother's Name :</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.mothersName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Date of Birth :</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.dateOfBirth}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" space-after="8pt">
                    <fo:table border="none">
                        <fo:table-column column-width="40mm" />
                        <fo:table-column column-width="85mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Father's/Guardian's Name:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.fathersName}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!-- Report Card Table -->
                <fo:block border-width="5mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" >
                    <fo:block th:text="${model.body.firstTable.title}"  font-size="9" font-weight="bold" font-family="Times New Roman, serif"></fo:block>
                    <fo:table border="1pt solid black">
                        <fo:table-column column-width="8mm"/>
                        <fo:table-column column-width="35mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-header font-size="9pt" >
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" number-rows-spanned="2" font-weight="bold">
                                    <fo:block>Sno</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" number-rows-spanned="2" font-weight="bold">
                                    <fo:block >Subject</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" number-columns-spanned="4" font-weight="bold">
                                    <fo:block>TERM I</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" number-columns-spanned="4" font-weight="bold">
                                    <fo:block>TERM II</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" number-columns-spanned="3" font-weight="bold">
                                    <fo:block>Overall TERM I + TERM II</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                      <fo:block th:text="${model.body.firstTable.column1}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm" >
                                    <fo:block th:text="${model.body.firstTable.column2}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm" >
                                    <fo:block th:text="${model.body.firstTable.column3}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm" >
                                    <fo:block th:text="${model.body.firstTable.column4}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column5}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column6}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column7}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column8}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column9}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column10}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column11}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                              <fo:table-row th:each="marks : ${model.body.firstTable.marks}">
                              <fo:table-cell border="1pt solid black" padding="1mm">
                                <fo:block th:text="${marks.sno}"></fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                <fo:block th:text="${marks.subject}"></fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding="1mm">
                                <fo:block th:text="${marks.pa1}"></fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding="1mm">
                                <fo:block th:text="${marks.pa2}"></fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding="1mm">
                                <fo:block th:text="${marks.hye}"></fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding="1mm">
                                <fo:block th:text="${marks.term1total}"></fo:block>
                            </fo:table-cell>
                                  <fo:table-cell border="1pt solid black" padding="1mm">
                                      <fo:block th:text="${marks.pa3}"></fo:block>
                                  </fo:table-cell>
                                  <fo:table-cell border="1pt solid black" padding="1mm">
                                      <fo:block th:text="${marks.pa4}"></fo:block>
                                  </fo:table-cell>
                                  <fo:table-cell border="1pt solid black" padding="1mm">
                                      <fo:block th:text="${marks.ye}"></fo:block>
                                  </fo:table-cell>
                                  <fo:table-cell border="1pt solid black" padding="1mm">
                                      <fo:block th:text="${marks.term2total}"></fo:block>
                                  </fo:table-cell>
                                  <fo:table-cell border="1pt solid black" padding="1mm">
                                      <fo:block th:text="${marks.pasTotal}"></fo:block>
                                  </fo:table-cell>
                                  <fo:table-cell border="1pt solid black" padding="1mm">
                                  <fo:block th:text="${marks.hyeYe}"></fo:block>
                                  </fo:table-cell>
                                  <fo:table-cell border="1pt solid black" padding="1mm">
                                      <fo:block th:text="${marks.termTotal}"></fo:block>
                                  </fo:table-cell>
                        </fo:table-row>
                            <fo:table-row  >
                                <fo:table-cell border="1pt solid black" font-size="small" text-align="left" height="5mm" number-columns-spanned="12">
                                    <fo:block>OVERALL GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-size="small">
                                    <fo:block th:text="${model.body.firstTable.totals.overallPercentage}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block border-width="1mm" font-size="9pt" text-align="center"  space-before="2mm"
                          th:if="${model.body.firstTable.external != null and #lists.size(model.body.firstTable.external) > 0}">
                    <fo:block font-size="9" font-weight="bold" text-align="left" font-family="Times New Roman, serif"
                              space-after="1mm"/>
                    <fo:table border="1pt solid black" >
                        <fo:table-column column-width="8mm"/>
                        <fo:table-column column-width="35mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>
                        <fo:table-column column-width="12mm"/>

                        <fo:table-body  font-family="Times New Roman, serif">
                            <fo:table-row th:each="external : ${model.body.firstTable.external}"  height="5mm">
                                <fo:table-cell border="1pt solid black">
                                    <fo:block th:text="${external.sno}" > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block th:text="${external.subject}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block th:text="${external.pa1}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block th:text="${external.pa2}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block th:text="${external.hye}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block th:text="${external.grade2}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block th:text="${external.pa3}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block th:text="${external.pa4}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-size="small">
                                    <fo:block th:text="${external.ye}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-size="small">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-size="small">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-size="small">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block font-family="Arial" font-size="12pt" text-align="center" font-weight="bold" space-before="2mm">
                    <fo:block space-after="0.1cm" font-weight="bold">PART II: Co-Scholastic Areas[on a 3-point(A to C) grading scale]</fo:block>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" >
                    <fo:table border="none" margin-left="18mm" >
                        <fo:table-column column-width="70mm" />
                        <fo:table-column column-width="35mm" />
                        <fo:table-column column-width="35mm" />
                        <fo:table-body text-align="left">
                            <fo:table-row height="5mm">
                                <fo:table-cell font-weight="bold" border="1pt solid black">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" >
                                    <fo:block >TERM I</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold">
                                    <fo:block >TERM II</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row height="5mm" th:each="marks : ${model.body.secondTable.marks}">
                                <fo:table-cell font-weight="bold" border="1pt solid black">
                                    <fo:block th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block th:text="${marks.term1Grade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block th:text="${marks.term2Grade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <!-- Attendance -->
                <fo:block border-width="1mm" font-size="10pt" space-after="3pt" font-family="Times New Roman, serif" >
                    <fo:block font-weight="bold"  font-family="Times New Roman, serif">Attendance&#160;:</fo:block>
                    <fo:table border="none">
                        <fo:table-column column-width="50mm"/>
                        <fo:table-column column-width="15mm"/>
                        <fo:table-column column-width="40mm"/>
                        <fo:table-column column-width="10mm"/>
                        <fo:table-column column-width="25mm"/>
                        <fo:table-column column-width="15mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell  height="5mm" >
                                    <fo:block>Total Number of Working days :</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.workingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  >
                                    <fo:block>Number of days Present  :</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.daysPresent}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block>Attendance(%) :</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.attendancePercentage}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" >
                    Remarks :
                    <fo:block-container font-size="9pt">
                        <fo:block border-bottom="0.2mm solid black" th:text="${model.body.attendance.remarks}">
                        </fo:block>
                    </fo:block-container>
                </fo:block>
                <!-- Signature Block-->
                <fo:block border-width="1mm" font-size="10pt" space-before="10pt" font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="50mm" />
                        <fo:table-column column-width="50mm" />
                        <fo:table-column column-width="50mm" />
                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold">
                                    <fo:block>Sr.Principal</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center" font-weight="bold">
                                    <fo:block>Head Mistress</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="right" font-weight="bold">
                                    <fo:block>Class Teacher</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="10pt" font-family="Times New Roman, serif" >
                    <fo:block th:replace="report-card/dps/fragment.xml :: ${model.body.gradingScale}"></fo:block>
                </fo:block>
                <fo:block  text-align="left">
                    PA :PERIODIC ASSESSMENT,
                    HYE :HALF YEARLY,
                    YE :YEARLY
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>
