<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format"
         xmlns:xlink="http://www.w3.org/1999/xlink">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="18mm"/>
        </fo:simple-page-master>
    </fo:layout-master-set>
    <!--first page -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm"  padding="6mm">

                <fo:block-container absolute-position="absolute" top="-45%" left="50%" width="0%" height="0%">
                    <fo:block th:if="${(model.header.gradeSlug matches 'iv|v')}">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial" >
                        <fo:instream-foreign-object content-width="285%" content-height="285%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon4thto6th.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                    </fo:block>
                    <fo:block th:if="${(model.header.gradeSlug matches 'vi|vii|viii')}">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial" >
                        <fo:instream-foreign-object content-width="285%" content-height="285%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon4thto6th.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none" width="100%" >
                    <fo:table-column column-width="22mm"/>
                    <fo:table-column column-width="150mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell display-align="center">
                                <fo:block text-align="left" padding-top="-10mm">
                                    <fo:external-graphic
                                            src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon%20bharati%20report%20card/doonlogo1.png")'
                                            content-width="70px"
                                            content-height="auto"
                                            scaling="uniform"/>
                                </fo:block>
                            </fo:table-cell>

                            <fo:table-cell display-align="center">
                                <fo:block text-align="center" padding-top="-22mm">
                                    <fo:instream-foreign-object content-width="100%" content-height="40mm">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" overflow="visible">
                                            <text x="50%" y="50%"
                                                  text-anchor="middle" dominant-baseline="middle"
                                                  font-size="25pt" font-family="Montserrat, Arial, Helvetica, sans-serif"  font-weight="bold"
                                                  transform="scale(1,1.7)">
                                                DOON BHARTI PUBLIC SCHOOL
                                            </text>
                                            <text x="40%" y="105%"
                                                  text-anchor="middle" dominant-baseline="middle" font-weight="bold"
                                                  font-size="16pt" font-family="Montserrat, Arial, Helvetica, sans-serif" >
                                                SEHATPUR
                                            </text>
                                        </svg>
                                    </fo:instream-foreign-object>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block text-align="center" font-size="25pt" font-family="Times New Roman, serif"  font-weight="bold" padding-top="-10mm">
                    PERFORMANCE PROFILE
                </fo:block>
                <fo:block  font-weight="bold" text-align="center"
                           font-size="12pt" font-family="Times New Roman, serif" padding-top="-2mm" >
                    Continuous &amp; Comprehensive Evaluation (CCE)
                </fo:block>
                <fo:block text-align="center" font-size="11pt" font-family="Montserrat, Arial, Helvetica, sans-serif" padding-top="5mm">
                    <fo:inline font-weight="bold">&#8226; Unit Test -1 </fo:inline>
                    <fo:inline padding-left="3mm" font-weight="bold">&#8226; Half Yearly Examination </fo:inline>
                    <fo:inline padding-left="3mm" font-weight="bold">&#8226; Unit Test -2 </fo:inline>
                    <fo:inline padding-left="3mm" font-weight="bold">&#8226; Yearly Examination</fo:inline>
                </fo:block>

                <fo:block text-align="center" font-size="13pt" font-weight="bold"
                          space-after="5mm" space-before="5mm">
                    Session 20<fo:inline-container inline-progression-dimension="20pt"
                                                   border-bottom="0.6pt solid black"
                                                   text-align="center"
                                                   display-align="center">
                    <fo:block border-bottom="0.6pt solid black" th:text="${model.header.sessionStart != null and model.header.sessionStart != '' ? model.header.sessionStart : ' '}">
                        &#160;
                    </fo:block>
                </fo:inline-container>
                    - 20<fo:inline-container inline-progression-dimension="20pt"
                                             border-bottom="0.6pt solid black"
                                             text-align="center"
                                             display-align="center">
                    <fo:block border-bottom="0.6pt solid black" th:text="${model.header.sessionEnd != null and model.header.sessionEnd != '' ? model.header.sessionEnd : ' '}">
                        &#160;
                    </fo:block>
                </fo:inline-container>
                    Class<fo:inline-container inline-progression-dimension="120pt"
                                              border-bottom="0.6pt solid black"
                                              text-align="center"
                                              display-align="center">
                    <fo:block border-bottom="0.6pt solid black" th:text="${model.header.className != null and model.header.className != '' ? model.header.className : ' '}">
                        &#160;
                    </fo:block>
                </fo:inline-container>
                    Section
                    <fo:inline-container inline-progression-dimension="80pt"
                                         border-bottom="0.6pt solid black"
                                         text-align="center"
                                         display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.sectionName != null and model.header.sectionName != '' ? model.header.sectionName : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                </fo:block>

                <fo:block-container border="2.5pt solid black" padding="5mm" margin-top="5mm" margin-left="13mm" padding-right="-10mm">
                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm">
                        Name : <fo:inline-container inline-progression-dimension="350pt"
                                                    border-bottom="0.6pt solid black"
                                                    text-align="center"
                                                    display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.studentName != null and model.header.studentName != '' ? model.header.studentName : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>

                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm">
                        Date of Birth : <fo:inline-container inline-progression-dimension="66pt"
                                                             border-bottom="0.6pt solid black"
                                                             text-align="center"
                                                             display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.dob != null and model.header.dob != '' ? model.header.dob : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                        Admission No. <fo:inline-container inline-progression-dimension="66pt"
                                                           border-bottom="0.6pt solid black"
                                                           text-align="center"
                                                           display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.admissionNumber != null and model.header.admissionNumber != '' ? model.header.admissionNumber : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                        Roll No. <fo:inline-container inline-progression-dimension="66pt"
                                                      border-bottom="0.6pt solid black"
                                                      text-align="center"
                                                      display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.rollNo != null and model.header.rollNo != '' ? model.header.rollNo : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>

                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm">
                        Father / Guardian’s Name : <fo:inline-container inline-progression-dimension="255pt"
                                                                        border-bottom="0.6pt solid black"
                                                                        text-align="center"
                                                                        display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.fatherName != null and model.header.fatherName != '' ? model.header.fatherName : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>

                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm">
                        Residence Address : <fo:inline-container inline-progression-dimension="285pt"
                                                                 border-bottom="0.6pt solid black"
                                                                 text-align="center"
                                                                 display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.address != null and model.header.address != '' ? model.header.address : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>

                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm">
                        <fo:inline-container inline-progression-dimension="390pt"
                                             border-bottom="0.6pt solid black"
                                             text-align="center"
                                             display-align="center">
                            <fo:block border-bottom="0.6pt solid black" >
                                &#160;
                            </fo:block>
                        </fo:inline-container>
                    </fo:block>

                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm">
                        Telephone Number : <fo:inline-container inline-progression-dimension="285pt"
                                                                border-bottom="0.6pt solid black"
                                                                text-align="center"
                                                                display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.phoneNumber != null and model.header.phoneNumber != '' ? model.header.phoneNumber : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>
                </fo:block-container>

                <fo:block text-align="left" padding-top="10mm" margin-left="-17mm"
                          th:if="${(model.header.gradeSlug matches 'iv|v')}">
                    <fo:external-graphic
                            src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/writinglogo.jpeg")'
                            content-width="590px"
                            content-height="auto"
                            scaling="uniform"/>
                </fo:block>
                <fo:block text-align="left" padding-top="10mm" margin-left="-17mm"
                          th:if="${(model.header.gradeSlug matches 'vi|vii|viii')}">
                    <fo:external-graphic
                            src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/writinglogo.jpeg")'
                            content-width="590px"
                            content-height="auto"
                            scaling="uniform"/>
                </fo:block>

                <fo:block font-size="15pt" font-weight="bold" padding-top="5pt" margin-left="380pt" color="#15113d"> Class :
                    <fo:inline >VI - VIII </fo:inline>
                </fo:block>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!--second page -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm"  padding="6mm">

                <fo:block-container absolute-position="absolute" top="-45%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial" th:if="${(model.header.gradeSlug matches 'vi|vii|viii')}">
                        <fo:instream-foreign-object content-width="285%" content-height="285%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon4thto6th.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial" th:if="${(model.header.gradeSlug matches 'iv|v')}">
                        <fo:instream-foreign-object content-width="285%" content-height="285%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon4thto6th.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:block margin-left="-8mm" padding-top="-5mm">
                    <fo:external-graphic src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon-logo-with-name.png")'
                                         content-width="50px" scaling="non-uniform"  />
                </fo:block>

                <fo:block-container absolute-position="absolute" top="-10mm" left="20mm" width="150mm">
                    <fo:block  font-size="15pt" font-weight="bold" text-align="center" space-after="5mm">
                        Session 20<fo:inline-container inline-progression-dimension="20pt"
                                                       border-bottom="0.6pt solid black"
                                                       text-align="center"
                                                       display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.sessionStart != null and model.header.sessionStart != '' ? model.header.sessionStart : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                        - 20<fo:inline-container inline-progression-dimension="20pt"
                                                 border-bottom="0.6pt solid black"
                                                 text-align="center"
                                                 display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.sessionEnd != null and model.header.sessionEnd != '' ? model.header.sessionEnd : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>
                    <fo:block  font-size="12pt">
                        Full Name:<fo:inline-container inline-progression-dimension="350pt"
                                                       border-bottom="0.6pt solid black"
                                                       text-align="center"
                                                       display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.studentName != null and model.header.studentName != '' ? model.header.studentName : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>
                    <fo:block  font-size="12pt" space-before="4mm" space-after="150mm">
                        Class: <fo:inline-container inline-progression-dimension="160pt"
                                                    border-bottom="0.6pt solid black"
                                                    text-align="center"
                                                    display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.className != null and model.header.className != '' ? model.header.className : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                        Section: <fo:inline-container inline-progression-dimension="160pt"
                                                      border-bottom="0.6pt solid black"
                                                      text-align="center"
                                                      display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.sectionName != null and model.header.sectionName != '' ? model.header.sectionName : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>
                </fo:block-container>

                <!-- Main Examination Table -->
                <fo:block-container absolute-position="absolute" top="25mm" left="-5mm" width="190mm"
                                    th:if="${model.body.firstTable.marks != null and #lists.size(model.body.firstTable.marks) > 0}">
                    <fo:table table-layout="fixed" border="1.5pt solid black" width="100%">

                        <!-- Define Columns -->
                        <fo:table-column column-width="45mm"/>
                        <!-- Half Yearly -->
                        <fo:table-column column-width="20mm"/>
                        <fo:table-column column-width="18mm"/>
                        <fo:table-column column-width="18.5mm"/>
                        <fo:table-column column-width="18.5mm"/>
                        <fo:table-column column-width="25mm"/>
                        <fo:table-column column-width="25mm"/>
                        <fo:table-column column-width="15mm"/>

                        <!-- Table Header -->
                        <fo:table-header>
                            <fo:table-row height="10mm">
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center" padding-top="2.5mm">Scholastic Area</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" number-columns-spanned="6">
                                    <fo:block text-align="center" font-weight="bold" padding-top="2.5mm">Half Yearly Examination</fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                            <!-- Sub-Headers -->
                            <fo:table-row text-align="center" height="10mm" >
                                <fo:table-cell border="1.5pt solid black"  number-rows-spanned="2"><fo:block padding-top="2.5mm">Subject Name</fo:block></fo:table-cell>
                                <!-- Half Yearly -->
                                <fo:table-cell border="1.5pt solid black" number-rows-spanned="2">
                                    <fo:block padding-top="2.5mm" th:if="${(model.header.gradeSlug matches 'vi|vii|viii')}">Unit Test-1 (05)</fo:block>
                                    <fo:block padding-top="2.5mm" th:if="${(model.header.gradeSlug matches 'iv|v')}">Unit Test-1 (20)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" number-rows-spanned="2">
                                    <fo:block padding-top="2.5mm">Port Folio (05)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" number-columns-spanned="2">
                                    <fo:block padding-top="2.5mm" >Subject Enrichment Activity</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" number-rows-spanned="2">
                                    <fo:block padding-top="2.5mm" th:if="${(model.header.gradeSlug matches 'vi|vii|viii')}">Half Yearly Exam (80)</fo:block>
                                    <fo:block padding-top="2.5mm" th:if="${(model.header.gradeSlug matches 'iv|v')}">Half Yearly Exam (60)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" number-rows-spanned="2">
                                    <fo:block padding-top="2.5mm">Total Out of 100</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" number-rows-spanned="2">
                                    <fo:block padding-top="2.5mm">Grade</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row text-align="center" height="10mm" >
                                <fo:table-cell border="1.5pt solid black"  >
                                    <fo:block padding-top="2.5mm" >CD (05)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black"  >
                                    <fo:block padding-top="2.5mm" th:if="${(model.header.gradeSlug matches 'vi|vii|viii')}">MA (05)</fo:block>
                                    <fo:block padding-top="2.5mm" th:if="${(model.header.gradeSlug matches 'iv|v')}">MA (10)</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <!-- Table Body (Subjects) -->
                        <fo:table-body>
                            <fo:table-row text-align="center" height="10mm" th:each="marks : ${model.body.firstTable.marks}">
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block padding-top="2.5mm" th:text="${marks.subjectName}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block padding-top="2.5mm" th:text="${marks.unitTest1}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block padding-top="2.5mm" th:text="${marks.portFolio}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block padding-top="2.5mm" th:text="${marks.cognitiveDevelopment}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block padding-top="2.5mm" th:text="${marks.ma}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block padding-top="2.5mm" th:text="${marks.halfYearly}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block padding-top="2.5mm" th:text="${marks.total}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block padding-top="2.5mm" th:text="${marks.grade}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block-container>


                <!-- Second Table: Remarks + Signatures -->
                <fo:block-container absolute-position="absolute" top="150mm" left="-5mm" width="190mm">
                    <fo:table table-layout="fixed" border="0.5pt solid black" width="100%">

                        <!-- Define Columns -->
                        <fo:table-column column-width="30mm"/>
                        <fo:table-column column-width="77.5mm"/>
                        <fo:table-column column-width="77.5mm"/>

                        <!-- Header Row -->
                        <fo:table-header>
                            <fo:table-row height="5mm">
                                <fo:table-cell border="1.5pt solid black"  number-rows-spanned="2">
                                    <fo:block text-align="center" font-weight="bold" padding-top="9mm">REMARKS</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block text-align="center" padding-top="0.7mm">Unit Test – 1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block text-align="center" padding-top="0.7mm">Half Yearly Exam</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row height="15mm">
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block text-align="center" font-weight="bold"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block text-align="center" font-weight="bold"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <!-- Empty Remarks Rows -->
                        <fo:table-body>
                            <fo:table-row height="10mm" text-align="center">
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block padding-top="1mm">Teacher’s Signature</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                                <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                            </fo:table-row>

                            <fo:table-row height="10mm" text-align="center">
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block padding-top="1mm">Supervisor’s Signature</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                                <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                            </fo:table-row>

                            <fo:table-row height="10mm" text-align="center">
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block padding-top="1mm">Incharge’s Signature</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                                <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                            </fo:table-row>

                            <fo:table-row height="10mm" text-align="center">
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block padding-top="1mm">Principal’s Signature</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                                <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                            </fo:table-row>

                            <fo:table-row height="10mm" text-align="center">
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block padding-top="1mm">Guardian’s Signature</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                                <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block-container>



            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- third page -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm"  padding="6mm">

                <fo:block-container absolute-position="absolute" top="-45%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial" th:if="${(model.header.gradeSlug matches 'vi|vii|viii')}">
                        <fo:instream-foreign-object content-width="285%" content-height="285%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon4thto6th.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial" th:if="${(model.header.gradeSlug matches 'iv|v')}">
                        <fo:instream-foreign-object content-width="285%" content-height="285%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon4thto6th.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:block margin-left="160mm" padding-top="-10mm">
                    <fo:external-graphic src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon-logo-with-name.png")'
                                         content-width="40px" scaling="non-uniform"  />
                </fo:block>

                <fo:block-container absolute-position="absolute" top="0mm" left="20mm" width="150mm">
                    <fo:block  font-size="15pt" font-weight="bold" text-align="center" space-after="5mm">
                        Session 20<fo:inline-container inline-progression-dimension="20pt"
                                                       border-bottom="0.6pt solid black"
                                                       text-align="center"
                                                       display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.sessionStart != null and model.header.sessionStart != '' ? model.header.sessionStart : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                        - 20<fo:inline-container inline-progression-dimension="20pt"
                                                 border-bottom="0.6pt solid black"
                                                 text-align="center"
                                                 display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.sessionEnd != null and model.header.sessionEnd != '' ? model.header.sessionEnd : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>
                </fo:block-container>

                <!-- Main Examination Table -->
                <fo:block-container absolute-position="absolute" top="12mm" left="-5mm" width="190mm">
                    <fo:table table-layout="fixed" border="1.5pt solid black" width="100%">

                        <!-- Define Columns -->
                        <fo:table-column column-width="130mm"/>
                        <fo:table-column column-width="55mm"/>

                        <!-- Table Header -->
                        <fo:table-header>
                            <fo:table-row >
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block  padding-top="2mm" margin-left="2mm" font-weight="bold">Child Care Development on 9 Point Grading Scale</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center" font-weight="bold" padding-top="2mm">Half Yearly Examination</fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                        </fo:table-header>

                        <!-- Table Body (Subjects) -->
                        <fo:table-body>
                            <fo:table-row th:each="marks : ${model.body.secondTable.marks}">
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block padding-top="2mm" margin-left="2mm" th:text="${marks.subjectName}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block text-align="center" padding-top="2mm" th:text="${marks.grade}"> </fo:block></fo:table-cell>
                            </fo:table-row>

                        </fo:table-body>
                    </fo:table>
                </fo:block-container>

                <!-- <fo:block>
                     <fo:block> Participated in internal Olympiad</fo:block>
                 </fo:block>-->


                <!-- ===== Footer Form Section ===== -->
                <fo:block-container padding-top="155mm">
                    <fo:block font-family="Arial" font-size="12pt" line-height="12pt" space-before="6mm">

                        <!-- Olympiad + Subjects (Yes/No with check boxes) -->
                        <fo:table table-layout="fixed" width="200mm" border="0pt">
                            <fo:table-column column-width="110mm"/>
                            <fo:table-column column-width="100mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell>
                                        <fo:block>
                                            <fo:inline>Participated in internal Olympiad</fo:inline>
                                            <fo:inline>
                                                <fo:leader leader-pattern="space" leader-length="25pt"/>
                                            </fo:inline>Yes
                                            <fo:inline border="0.8pt solid #000" width="8pt" height="11pt" font-family="ZapfDingbats"
                                                       th:if="${model.body.isParticipatedInInternal == 'true'}" text-align="center" display-align="center"> ✔
                                            </fo:inline>
                                            <fo:inline border="0.8pt solid #000" width="11pt" height="11pt" font-family="ZapfDingbats"
                                                       th:if="${model.body.isParticipatedInInternal == 'false'}"> &#160;&#160;
                                            </fo:inline>
                                            <fo:inline>
                                                <fo:leader leader-pattern="space" leader-length="25pt"/>
                                            </fo:inline>No
                                            <fo:inline border="0.8pt solid #000" width="11pt" height="11pt" font-family="ZapfDingbats"
                                                       th:if="${model.body.isParticipatedInInternal == 'true'}" text-align="center" display-align="center"> &#160;&#160;
                                            </fo:inline>
                                            <fo:inline border="0.8pt solid #000" width="11pt" height="11pt" font-family="ZapfDingbats"
                                                       th:if="${model.body.isParticipatedInInternal == 'false'}"> ✔
                                            </fo:inline>
                                        </fo:block>
                                        <fo:block space-before="2mm">
                                            <fo:inline>Participated in Olympiad</fo:inline>
                                            <fo:inline>
                                                <fo:leader leader-pattern="space" leader-length="62pt"/>
                                            </fo:inline>
                                            Yes
                                            <fo:inline border="0.8pt solid #000" width="8pt" height="11pt" font-family="ZapfDingbats"
                                                       th:if="${model.body.isParticipated == 'true'}" text-align="center" display-align="center"> ✔
                                            </fo:inline>
                                            <fo:inline border="0.8pt solid #000" width="11pt" height="11pt" font-family="ZapfDingbats"
                                                       th:if="${model.body.isParticipated == 'false'}"> &#160;&#160;
                                            </fo:inline>
                                            <fo:inline>
                                                <fo:leader leader-pattern="space" leader-length="25pt"/>
                                            </fo:inline>No
                                            <fo:inline border="0.8pt solid #000" width="8pt" height="11pt" font-family="ZapfDingbats"
                                                       th:if="${model.body.isParticipated == 'true'}" text-align="center" display-align="center"> &#160;&#160;
                                            </fo:inline>
                                            <fo:inline border="0.8pt solid #000" width="11pt" height="11pt" font-family="ZapfDingbats"
                                                       th:if="${model.body.isParticipated == 'false'}"> ✔
                                            </fo:inline>
                                        </fo:block>
                                    </fo:table-cell>

                                    <fo:table-cell>
                                        <fo:block>
                                            Subjects
                                        <fo:inline-container width="80pt">
                                            <fo:block border-bottom="0.6pt dotted black"
                                                      th:text="${model.body.olympiadSubjects}">&#160;</fo:block>
                                        </fo:inline-container>
                                        </fo:block>
                                        <fo:block space-before="2mm">
                                            Subjects
                                            <fo:inline-container width="80pt">
                                                <fo:block border-bottom="0.6pt dotted black"
                                                          th:text="${model.body.internalOlympiadSubjects}">&#160;</fo:block>
                                            </fo:inline-container>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>

                        <!-- Attendance small table -->

                        <fo:table table-layout="fixed" width="180mm" space-before="6mm" font-size="9pt">
                            <fo:table-column column-width="35mm"/>
                            <fo:table-column column-width="45mm"/>
                            <fo:table-column column-width="20mm"/>
                            <fo:table-column column-width="80mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell>
                                        <fo:block border="0.8pt solid #000"  font-weight="bold" text-align="center">
                                            Total Working Days
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block border="0.8pt solid #000"  font-weight="bold" text-align="center">
                                            Total Attendance By Student
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell font-size="11pt">
                                        <fo:block>
                                            Health Status : Height <fo:inline border-bottom="0.6pt dotted black" padding-left="8pt" padding-right="8pt"
                                                                              th:text="${model.body.height != null and model.body.height != '' ? model.body.height : ' '}"> &#160;</fo:inline>
                                            &#160;&#160;&#160;&#160;<fo:inline border-bottom="0.6pt dotted black" padding-left="8pt" padding-right="8pt"
                                                                               th:text="${model.body.height != null and model.body.height != '' ? model.body.height : ' '}"> &#160;</fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row>
                                    <fo:table-cell>
                                        <fo:block border="0.8pt solid #000"  font-weight="bold" text-align="center"
                                                  th:text="${model.body.totalWorkingDays}">
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block border="0.8pt solid #000"  font-weight="bold" text-align="center"
                                                  th:text="${model.body.totalPresentDays}">
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell font-size="11pt">
                                        <fo:block margin-left="24mm">
                                            weight  <fo:inline border-bottom="0.6pt dotted black" padding-left="8pt" padding-right="8pt"
                                                               th:text="${model.body.height != null and model.body.height != '' ? model.body.height : ' '}"> &#160;</fo:inline>
                                            &#160;&#160;&#160;&#160;<fo:inline border-bottom="0.6pt dotted black" padding-left="8pt" padding-right="8pt"
                                                                               th:text="${model.body.height != null and model.body.height != '' ? model.body.height : ' '}"> &#160;</fo:inline>
                                        </fo:block>

                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>

                        <!-- Overall Remarks -->
                        <fo:block th:if="${model.body.term == 't2'}">
                        <fo:block space-before="6mm" >
                            Overall Remarks (Year End)<fo:inline border-bottom="0.6pt dotted black" padding-left="162pt" padding-right="162pt"
                                                                 th:text="${model.body.overallRemark != null and model.body.overallRemark != '' ? model.body.overallRemark : ' '}"> &#160;</fo:inline>
                        </fo:block>
                        </fo:block>
                    <fo:block th:if="${model.body.term == 't2'}">
                        <fo:block space-before="2mm" space-after="2mm">
                            <fo:inline border-bottom="0.6pt dotted black" padding-left="243pt" padding-right="243pt"
                            > &#160;</fo:inline>
                        </fo:block>
                    </fo:block>

                        <!-- Promotion + Signatures row -->
                        <fo:table table-layout="fixed" width="180mm" space-before="12mm">
                            <fo:table-column column-width="96mm"/>
                            <fo:table-column column-width="42mm"/>
                            <fo:table-column column-width="42mm"/>
                            <fo:table-body>
                                <fo:table-row>

                                    <!-- Promotion text with check box -->
                                    <fo:table-cell>
                                        <fo:block th:if="${model.body.term == 't2'}">
                                            Promoted to class
                                            <fo:leader leader-pattern="dots" leader-length="35%"/>
                                        </fo:block>
                                        <fo:block th:if="${model.body.term == 't2'}" space-before="2mm" font-size="10pt">
                                            Or Eligible for improvement of performance
                                            <fo:inline padding-left="6pt"/>
                                            <fo:inline border="0.8pt solid #000" width="11pt" height="11pt" font-family="ZapfDingbats">
                                                <fo:leader leader-pattern="space" leader-length="9pt"/>
                                            </fo:inline>
                                        </fo:block>
                                        <fo:block th:if="${model.body.term != 't2'}"> </fo:block>
                                    </fo:table-cell>

                                    <!-- Class teacher sign -->
                                    <fo:table-cell>
                                        <fo:block text-align="center" space-before="6mm" >
                                            <fo:leader leader-pattern="dots" leader-length="90%"/>
                                        </fo:block>
                                        <fo:block text-align="center" font-size="10pt" space-before="1mm" >
                                            Class teacher’s Signature
                                        </fo:block>
                                    </fo:table-cell>

                                    <!-- Principal sign -->
                                    <fo:table-cell>
                                        <fo:block text-align="center" space-before="6mm">
                                            <fo:leader leader-pattern="dots" leader-length="90%"/>
                                        </fo:block>
                                        <fo:block text-align="center" font-size="10pt" space-before="1mm">
                                            Principal Signature
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block-container>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--fourth page -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm"  padding="6mm">

                <!-- Watermark container -->
                <fo:block-container absolute-position="absolute" top="-45%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial" th:if="${(model.header.gradeSlug matches 'vi|vii|viii')}">
                        <fo:instream-foreign-object content-width="285%" content-height="285%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon4thto6th.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial" th:if="${(model.header.gradeSlug matches 'iv|v')}">
                        <fo:instream-foreign-object content-width="285%" content-height="285%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon4thto6th.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>

                <fo:block-container absolute-position="absolute" left="36mm" top="-7mm"
                                    width="100mm" height="18mm" text-align="center">
                    <fo:block>
                        <fo:instream-foreign-object>
                            <svg xmlns="http://www.w3.org/2000/svg" width="100mm" height="18mm" viewBox="0 0 1000 240">
                                <!-- Wider pill with rounded corners -->
                                <rect x="0" y="0" width="1000" height="180" rx="90" ry="90" fill="#15113d"/>
                                <!-- Text with breathing space -->
                                <text x="50%" y="50%"
                                      dominant-baseline="middle" text-anchor="middle"
                                      font-family="Times New Roman" font-size="100" font-weight="bold" fill="white">
                                    INFORMATION
                                </text>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>

                <fo:block  font-size="12pt" line-height="14pt" space-before="12mm" space-after="15pt">
                    • This Learning Voyage is a continuous &amp; comprehensive assessment of child through out the year in scholastic &amp; co-scholastic field.
                </fo:block>

                <fo:block  font-size="12pt" line-height="14pt" space-after="15pt">
                    • We at DBPS, believe in preparing students to succeed in life. Discipline, punctuality &amp; smart hard work are the pillars of success attained through passion, commitment &amp; dedication.
                </fo:block>

                <fo:block  font-size="12pt" line-height="14pt" space-after="15pt">
                    • This Learning Voyage is an important document related to your ward &amp; it is required to be sent back to school with in two working days of its receipt.
                </fo:block>

                <fo:block  font-size="12pt" line-height="14pt" space-after="15pt">
                    • Scholastic areas are judged in 9 point grading system i.e A1, A2, B1, B2, C1, C2, D, E1 &amp; E2
                </fo:block>

                <!-- Grading Table -->
                <!-- Grading Table Centered -->
                <fo:block margin-top="8pt" margin-bottom="8pt" margin-left="auto" margin-right="auto">
                    <fo:table
                            table-layout="fixed"
                            width="60mm"
                            margin-left="auto"
                            margin-right="auto">
                        <fo:table-column column-width="45mm"/>
                        <fo:table-column column-width="30mm"/>
                        <fo:table-column column-width="60mm"/>
                        <fo:table-body>

                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" text-align="center"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black" >
                                    <fo:block font-weight="bold" text-align="center" padding-top="1mm">Grade</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black">
                                    <fo:block font-weight="bold" text-align="center" padding-top="1mm">Marks Range (%)</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block padding-top="1mm" font-weight="bold" text-align="center"> </fo:block></fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">A1</fo:block></fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">91-100</fo:block></fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell> <fo:block padding-top="1mm" font-weight="bold" text-align="center"> </fo:block></fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">A2</fo:block></fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">81-90</fo:block></fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell><fo:block font-weight="bold" text-align="center"> </fo:block> </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">B1</fo:block></fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">71-80</fo:block></fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell><fo:block padding-top="1mm" font-weight="bold" text-align="center"> </fo:block> </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">B2</fo:block></fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">61-70</fo:block></fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell><fo:block padding-top="1mm" font-weight="bold" text-align="center"> </fo:block> </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">C1</fo:block></fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">51-60</fo:block></fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell><fo:block padding-top="1mm" font-weight="bold" text-align="center"> </fo:block> </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">C2</fo:block></fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">41-50</fo:block></fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell> <fo:block padding-top="1mm" font-weight="bold" text-align="center"> </fo:block></fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">D</fo:block></fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">33-40</fo:block></fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell><fo:block padding-top="1mm" font-weight="bold" text-align="center"> </fo:block> </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">E1</fo:block></fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">21-32</fo:block></fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell> <fo:block padding-top="1mm" font-weight="bold" text-align="center"> </fo:block></fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">E2</fo:block></fo:table-cell>
                                <fo:table-cell border="0.5pt solid black"><fo:block padding-top="1mm" text-align="center">0-20 Absent</fo:block></fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>



                <fo:block  font-size="12pt" line-height="14pt" space-after="15pt" space-before="5mm">
                    • Co-scholastic areas are also assessed under 9 point grading system as above.
                </fo:block>

                <fo:block  font-size="12pt" line-height="14pt" space-after="15pt">
                    • At DBPS, we work at grass-root level through our Connecting The Roots Program which encircles learning of Music, Dance, ABACUS, Art &amp; Craft, Drawing, Meditation, Vedic Shiksha, Sports, Games, Projects, Practical experiences, Labs, various digital &amp; 5 senses oriented activities in academic &amp; co-curricular field.
                </fo:block>

                <!-- Signature Lines -->
                <fo:block-container margin-top="20mm" width="100%">
                    <fo:table width="100%">
                        <fo:table-column column-width="50%"/>
                        <fo:table-column column-width="50%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell text-align="center">
                                    <fo:block>.........................................</fo:block>
                                    <fo:block font-size="10pt">Specimen Signature of Mother</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center">
                                    <fo:block>.....................................................</fo:block>
                                    <fo:block font-size="10pt">Specimen Signature of Father / Guardian</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block-container>

                <!-- Contact -->
                <fo:block text-align="center" margin-top="30mm">
                    <fo:inline
                            font-size="16pt"
                            font-weight="bold"
                            color="#1a2a4d"
                            border="1pt solid #1a2a4d"
                            padding="4pt"
                            padding-top="2mm">
                        CONTACT NO.: 9533 000 123
                    </fo:inline>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

</fo:root>
