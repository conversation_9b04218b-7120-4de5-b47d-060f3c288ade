<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:xlink="http://www.w3.org/1999/xlink">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="12mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block  padding="10mm" margin-top="-1.5cm">

                <fo:table  width="100%">
                    <fo:table-column column-width="100%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block font-size="10pt" font-weight="bold" text-align="center" padding="1mm" th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold"  text-align="center" > SESSION :
                                    <fo:inline  th:text="${model.header.academicYear}"> </fo:inline>
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold"  text-align="center"  th:text="${model.header.testName}">
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold"  text-align="center" > CLASS -
                                    <fo:inline  th:text="${model.header.section}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:table  border="none" font-size="9pt" space-before="5pt" space-after="5pt">
                    <fo:table-column column-width="90mm"/>
                    <fo:table-column column-width="70mm"/>
                    <fo:table-column column-width="40mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block padding-top="4pt" font-weight="bold">SUBJECT :
                                    <fo:inline font-weight="normal" th:text="${model.body.subject}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block padding-top="4pt" font-weight="bold">M.M. :
                                    <fo:inline font-weight="normal" th:text="${model.body.totalMarks}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" >

                    <fo:table border="1pt solid black"  >
                        <fo:table-column column-width="9mm" />
                        <fo:table-column column-width="25mm" />
                        <fo:table-column column-width="45mm" />
                        <fo:table-column th:attr="column-width=${model.body.firstTable.column4 == null ? '30mm' : (model.body.firstTable.column5 == null ? '22.5mm' : '18mm')}" />
                        <fo:table-column th:attr="column-width=${model.body.firstTable.column4 == null ? '30mm' : (model.body.firstTable.column5 == null ? '22.5mm' : '18mm')}" />
                        <fo:table-column th:attr="column-width=${model.body.firstTable.column4 == null ? '30mm' : (model.body.firstTable.column5 == null ? '22.5mm' : '18mm')}" />
                        <fo:table-column th:if="${model.body.firstTable.column4 != null}"
                                         th:attr="column-width=${model.body.firstTable.column4 == null ? '30mm' : (model.body.firstTable.column5 == null ? '22.5mm' : '18mm')}" />
                        <fo:table-column th:if="${model.body.firstTable.column5 != null}"
                                         th:attr="column-width='18mm'" /> />
                        <fo:table-column column-width="18mm" />

                        <fo:table-header font-size="9pt">
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-rows-spanned="2" text-align="center" padding-top="8mm">
                                    <fo:block>S.NO</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" number-rows-spanned="2" padding-top="8mm">
                                    <fo:block>Admission No </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" number-rows-spanned="2" padding-top="8mm">
                                    <fo:block>Name of the Students </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                                               th:attr="number-columns-spanned=${model.body.firstTable.column5 != null ? 6 : (model.body.firstTable.column4 != null ? 5 : 4)}">
                                    <fo:block>Checking</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column1}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column2}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column3}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell th:if="${model.body.firstTable.column4 != null}" border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column4}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell th:if="${model.body.firstTable.column5 != null}" border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column5}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.total}">  </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body >
                            <fo:table-row th:each="i : ${#numbers.sequence(0,25)}" height="6mm" >
                                <fo:table-cell border="1pt solid black">
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].sno} : ''">
                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell border="1pt solid black"  text-align="left" padding-left="2mm">
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].admissionNo} : ''"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  text-align="left" padding-left="2mm">
                                    <fo:block  padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                               th:text="${model.body.firstTable.marks.size() > i} ? ${#strings.toUpperCase(model.body.firstTable.marks[i].name)} : ''"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].sec1} : ''"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].sec2} : ''"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].sec3} : ''"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" th:if="${model.body.firstTable.column4 != null}">
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].sec4} : ''"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" th:if="${model.body.firstTable.column5 != null}">
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].sec5} : ''"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].total} : ''"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>

                    </fo:table>
                </fo:block>


                <fo:table border="none" font-size="10pt"  space-after="5pt" space-before="20pt">
                    <fo:table-column column-width="38mm"/>
                    <fo:table-column column-width="50mm"/>
                    <fo:table-column column-width="5mm"/>
                    <fo:table-column column-width="45mm"/>
                    <fo:table-column column-width="50mm"/>
                    <fo:table-body>
                        <fo:table-row space-after="5pt">
                            <fo:table-cell height="5mm" margin-bottom="5mm">
                                <fo:block font-weight="bold">Name of the teacher :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black" >
                                <fo:block >
                                    <fo:inline  font-weight="normal" th:text="${model.body.teacherName}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Name of the Re-checker :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block>
                                    <fo:inline font-weight="normal" th:text="${model.body.reCheckerName}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" font-size="10pt" space-before="5pt" space-after="5pt">
                    <fo:table-column column-width="13mm"/>
                    <fo:table-column column-width="30mm"/>
                    <fo:table-column column-width="50mm"/>
                    <fo:table-column column-width="47mm"/>
                    <fo:table-column column-width="30mm"/>
                    <fo:table-body>
                        <fo:table-row space-after="5pt">
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Mean :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block text-align="center">
                                    <fo:inline font-weight="normal" th:text="${model.body.mean}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Range(H.Score - L.Score) :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block text-align="center">
                                    <fo:inline font-weight="normal" th:text="${model.body.range}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" font-size="10pt" space-before="5pt" space-after="6pt">
                    <fo:table-column column-width="35mm"/>
                    <fo:table-column column-width="30mm"/>
                    <fo:table-column column-width="28mm"/>
                    <fo:table-column column-width="15mm"/>
                    <fo:table-column column-width="30mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Student Appeared :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block text-align="center">
                                    <fo:inline font-weight="normal" th:text="${model.body.studentAppeared}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Failed :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block text-align="center">
                                    <fo:inline font-weight="normal" th:text="${model.body.failed}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" font-size="10pt" space-before="5pt" space-after="6pt">
                    <fo:table-column column-width="39mm"/>
                    <fo:table-column column-width="48mm"/>
                    <fo:table-column column-width="6mm"/>
                    <fo:table-column column-width="27mm"/>
                    <fo:table-column column-width="50mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Sign of Sub. Teacher :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block>
                                    <fo:inline font-weight="normal" th:text="${model.body.subTeacher}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Sign of H.O.D :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block>
                                    <fo:inline font-weight="normal" th:text="${model.body.signOfHOD}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" font-size="10pt" space-before="5pt" space-after="5pt">
                    <fo:table-column column-width="55mm"/>
                    <fo:table-column column-width="33mm"/>
                    <fo:table-column column-width="5mm"/>
                    <fo:table-column column-width="33mm"/>
                    <fo:table-column column-width="45mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Sign of Examination Incharge :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block>
                                    <fo:inline font-weight="normal" th:text="${model.body.signOfInCharge}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Sign Of Principal :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block>
                                    <fo:inline font-weight="normal" th:text="${model.body.signOfPrincipal}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block>
        </fo:flow>
    </fo:page-sequence>

    <!-- second page -->
    <fo:page-sequence master-reference="invoice" th:if="${model.body.firstTable.marks.size() > 26}">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block  padding="10mm" margin-top="-1.5cm">

                <fo:table  width="100%">
                    <fo:table-column column-width="100%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block font-size="10pt" font-weight="bold" text-align="center" padding="1mm" th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold"  text-align="center" > SESSION :
                                    <fo:inline  th:text="${model.header.academicYear}"> </fo:inline>
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold"  text-align="center"  th:text="${model.header.testName}">
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold"  text-align="center" > CLASS -
                                    <fo:inline  th:text="${model.header.section}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:table  border="none" font-size="9pt" space-before="5pt" space-after="5pt">
                    <fo:table-column column-width="90mm"/>
                    <fo:table-column column-width="70mm"/>
                    <fo:table-column column-width="40mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block padding-top="4pt" font-weight="bold">SUBJECT :
                                    <fo:inline font-weight="normal" th:text="${model.body.subject}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block padding-top="4pt" font-weight="bold">M.M. :
                                    <fo:inline font-weight="normal" > 40</fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" >

                    <fo:table border="1pt solid black"  >
                        <fo:table-column column-width="9mm" />
                        <fo:table-column column-width="25mm" />
                        <fo:table-column column-width="45mm" />
                        <fo:table-column th:attr="column-width=${model.body.firstTable.column4 == null ? '30mm' : (model.body.firstTable.column5 == null ? '22.5mm' : '18mm')}" />
                        <fo:table-column th:attr="column-width=${model.body.firstTable.column4 == null ? '30mm' : (model.body.firstTable.column5 == null ? '22.5mm' : '18mm')}" />
                        <fo:table-column th:attr="column-width=${model.body.firstTable.column4 == null ? '30mm' : (model.body.firstTable.column5 == null ? '22.5mm' : '18mm')}" />
                        <fo:table-column th:if="${model.body.firstTable.column4 != null}"
                                         th:attr="column-width=${model.body.firstTable.column4 == null ? '30mm' : (model.body.firstTable.column5 == null ? '22.5mm' : '18mm')}" />
                        <fo:table-column th:if="${model.body.firstTable.column5 != null}"
                                         th:attr="column-width='18mm'" /> />
                        <fo:table-column column-width="18mm" />

                        <fo:table-header font-size="9pt">
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-rows-spanned="2" text-align="center" padding-top="8mm">
                                    <fo:block>S.NO</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" number-rows-spanned="2" padding-top="8mm">
                                    <fo:block>Admission No </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" number-rows-spanned="2" padding-top="8mm">
                                    <fo:block>Name of the Students </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"
                                               th:attr="number-columns-spanned=${model.body.firstTable.column5 != null ? 6 : (model.body.firstTable.column4 != null ? 5 : 4)}">
                                    <fo:block>Checking</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column1}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column2}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column3}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell th:if="${model.body.firstTable.column4 != null}" border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column4}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell th:if="${model.body.firstTable.column5 != null}" border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column5}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.total}">  </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body >
                            <fo:table-row th:each="i : ${#numbers.sequence(26,51)}" height="6mm" >
                                <fo:table-cell border="1pt solid black">
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].sno} : ''">
                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell border="1pt solid black"  text-align="left" padding-left="2mm">
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].admissionNo} : ''"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  text-align="left" padding-left="2mm">
                                    <fo:block  padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                               th:text="${model.body.firstTable.marks.size() > i} ? ${#strings.toUpperCase(model.body.firstTable.marks[i].name)} : ''"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].sec1} : ''"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].sec2} : ''"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].sec3} : ''"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" th:if="${model.body.firstTable.column4 != null}">
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].sec4} : ''"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" th:if="${model.body.firstTable.column5 != null}">
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].sec5} : ''"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="1mm" padding-bottom="1mm" padding-left="1mm" padding-right="1mm"
                                              th:text="${model.body.firstTable.marks.size() > i} ? ${model.body.firstTable.marks[i].total} : ''"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>

                    </fo:table>
                </fo:block>


                <fo:table border="none" font-size="10pt"  space-after="5pt" space-before="20pt">
                    <fo:table-column column-width="38mm"/>
                    <fo:table-column column-width="50mm"/>
                    <fo:table-column column-width="5mm"/>
                    <fo:table-column column-width="45mm"/>
                    <fo:table-column column-width="50mm"/>
                    <fo:table-body>
                        <fo:table-row space-after="5pt">
                            <fo:table-cell height="5mm" margin-bottom="5mm">
                                <fo:block font-weight="bold">Name of the teacher :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black" >
                                <fo:block >
                                    <fo:inline  font-weight="normal" th:text="${model.body.teacherName}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Name of the Re-checker :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block>
                                    <fo:inline font-weight="normal" th:text="${model.body.reCheckerName}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" font-size="10pt" space-before="5pt" space-after="5pt">
                    <fo:table-column column-width="13mm"/>
                    <fo:table-column column-width="30mm"/>
                    <fo:table-column column-width="50mm"/>
                    <fo:table-column column-width="47mm"/>
                    <fo:table-column column-width="30mm"/>
                    <fo:table-body>
                        <fo:table-row space-after="5pt">
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Mean :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block text-align="center">
                                    <fo:inline font-weight="normal" th:text="${model.body.mean}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Range(H.Score - L.Score) :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block text-align="center">
                                    <fo:inline font-weight="normal" th:text="${model.body.range}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" font-size="10pt" space-before="5pt" space-after="6pt">
                    <fo:table-column column-width="35mm"/>
                    <fo:table-column column-width="30mm"/>
                    <fo:table-column column-width="28mm"/>
                    <fo:table-column column-width="15mm"/>
                    <fo:table-column column-width="30mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Student Appeared :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block text-align="center">
                                    <fo:inline font-weight="normal" th:text="${model.body.studentAppeared}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Failed :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block text-align="center">
                                    <fo:inline font-weight="normal" th:text="${model.body.failed}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" font-size="10pt" space-before="5pt" space-after="6pt">
                    <fo:table-column column-width="39mm"/>
                    <fo:table-column column-width="48mm"/>
                    <fo:table-column column-width="6mm"/>
                    <fo:table-column column-width="27mm"/>
                    <fo:table-column column-width="50mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Sign of Sub. Teacher :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block>
                                    <fo:inline font-weight="normal" th:text="${model.body.subTeacher}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Sign of H.O.D :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block>
                                    <fo:inline font-weight="normal" th:text="${model.body.signOfHOD}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" font-size="10pt" space-before="5pt" space-after="5pt">
                    <fo:table-column column-width="55mm"/>
                    <fo:table-column column-width="33mm"/>
                    <fo:table-column column-width="5mm"/>
                    <fo:table-column column-width="33mm"/>
                    <fo:table-column column-width="45mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Sign of Examination Incharge :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block>
                                    <fo:inline font-weight="normal" th:text="${model.body.signOfInCharge}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm">
                                <fo:block font-weight="bold">Sign Of Principal :</fo:block>
                            </fo:table-cell>
                            <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                <fo:block>
                                    <fo:inline font-weight="normal" th:text="${model.body.signOfPrincipal}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block>
        </fo:flow>
    </fo:page-sequence>

</fo:root>