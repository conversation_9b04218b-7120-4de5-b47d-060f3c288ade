<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:xlink="http://www.w3.org/1999/xlink">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice" page-height="210mm" page-width="297mm">
            <fo:region-body margin="18mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block  padding="12mm"  margin-top="-1.3cm" font-size="14pt" >
                <fo:block font-weight="bold">
                    <fo:inline font-weight="bold" padding-right="85mm">Parent Copy</fo:inline>
                    <fo:inline font-weight="bold" padding-right="70mm">RECEIPT</fo:inline>
                    <fo:inline font-weight="bold" text-align="left">Receipt Number: 1234</fo:inline>
                </fo:block>
                <fo:block border="solid 2pt #CFCFCF" space-before="5mm" padding-bottom="2mm">
                    <fo:block>
                        <fo:table border="none">
                            <fo:table-column column-width="65mm" />
                            <fo:table-column column-width="125mm" />
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell>
                                        <fo:block margin-left="5mm">
                                            <fo:external-graphic
                                                    th:src="|url('${model.header.logoUrl}')|"
                                                    content-width="60px"
                                                    content-height="60px"/>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="7mm" color="#2C5D87">
                                        <fo:block font-size="15pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center" space-after="2pt"
                                                  th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                        </fo:block >
                                        <fo:block padding-top="-3mm" font-size="13mm">
                                        <fo:block font-size="12pt" font-weight="bold" text-align="center"
                                                  th:replace="report-card/dps/fragment.xml :: ${model.header.orgSlug}"> </fo:block>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:block margin-left="2mm" color="#CFCFCF" padding-top="-3mm"> ------------------------------------------------------------------------------------------------------------------------------------------------------------</fo:block>

                        <fo:table  border="none" font-size="12pt" space-before="5pt" space-after="5pt" margin-left="2mm">
                            <fo:table-column column-width="120mm"/>
                            <fo:table-column column-width="50mm"/>
                            <fo:table-column column-width="70mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell>
                                        <fo:block padding-top="4pt" font-weight="normal"> Student :
                                            <fo:inline font-weight="bold" th:text="${#strings.toUpperCase(model.header.studentName)}"> </fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block padding-top="4pt" font-weight="normal"> Class :
                                            <fo:inline font-weight="bold" th:text="${model.header.className}"> </fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row>
                                    <fo:table-cell>
                                        <fo:block padding-top="4pt" font-weight="normal"> Father :
                                            <fo:inline font-weight="bold" th:text="${#strings.toUpperCase(model.header.fatherName)}"> </fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block padding-top="4pt" font-weight="normal"> Student Reg No :
                                            <fo:inline font-weight="bold" th:text="${model.header.rollNo}"> </fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>

                        <fo:table  border="solid 1pt black" font-size="12pt" space-before="15pt" space-after="5pt" margin-left="2.5mm">
                            <fo:table-column column-width="15mm"/>
                            <fo:table-column column-width="170mm"/>
                            <fo:table-column column-width="70mm"/>
                            <fo:table-header>
                                <fo:table-row >
                                    <fo:table-cell border="solid 1pt black">
                                        <fo:block padding-top="4pt" font-weight="bold" padding-bottom="2pt" text-align="left"> S.No </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="solid 1pt black">
                                        <fo:block padding-top="4pt" font-weight="bold" padding-bottom="2pt" text-align="left">Description </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  border="solid 1pt black">
                                        <fo:block padding-top="4pt" font-weight="bold" padding-bottom="2pt" text-align="center"> Amount </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body >
                                <fo:table-row th:each="marks : ${model.body.firstTable.marks}">
                                    <fo:table-cell border="solid 1pt black">
                                        <fo:block margin-left="3mm" padding-top="4pt"  padding-bottom="2pt"
                                                  th:text="${marks.sno}">  </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="solid 1pt black">
                                        <fo:block padding-top="4pt" font-weight="normal" padding-bottom="2pt" text-align="left"
                                                  th:text="${marks.description}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  border="solid 1pt black">
                                        <fo:block padding-top="4pt" font-weight="normal" padding-bottom="2pt" text-align="center"
                                                  >Rs. <fo:inline th:text="${marks.amount}"> </fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row >
                                    <fo:table-cell border="solid 1pt black" number-columns-spanned="2">
                                        <fo:block padding-top="4pt" font-weight="bold" padding-bottom="2pt" text-align="right" margin-right="2mm"
                                        > Total</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  border="solid 1pt black">
                                        <fo:block padding-top="4pt" font-weight="normal" padding-bottom="2pt" text-align="center"
                                                  >Rs.
                                            <fo:inline th:text="${model.body.firstTable.totalMoney}"> </fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>

                        <fo:block space-before="4mm" margin-left="3.5mm" font-weight="bold" font-size="13pt" th:text="${model.body.firstTable.totalMoneyText}"> </fo:block>

                        <fo:table  border="none" font-size="12pt" space-before="5pt" space-after="5pt" margin-left="1.8mm">
                            <fo:table-column column-width="150mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell>
                                        <fo:block padding-top="4pt" font-weight="normal"> Mode of Payment &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:
                                            <fo:inline font-weight="normal" th:text="${model.body.paymentType}"> </fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row>
                                    <fo:table-cell>
                                        <fo:block padding-top="4pt" font-weight="normal"> Payment Date &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:
                                            <fo:inline font-weight="normal" th:text="${model.body.receiptDate}"> </fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:block font-size="12pt" space-before="10mm">
                            <fo:inline  padding-right="180mm" padding-left="3mm">Parent's Signature</fo:inline>
                            <fo:inline > Received By</fo:inline>
                        </fo:block>
                        <fo:block font-size="12pt" space-before="10mm">
                            <fo:inline  padding-left="3mm">Receipt Date : </fo:inline>
                            <fo:inline th:text="${model.body.receiptDate}" padding-right="138mm"> </fo:inline>
                            <fo:inline padding-left="20mm"> Cashier's Signature </fo:inline>
                        </fo:block>

                        <fo:block font-weight="bold" font-size="13pt" space-before="2mm" margin-left="3mm"> Fee once paid is non-refundable except for Caution Money.</fo:block>

                    </fo:block>
                </fo:block>
            </fo:block>
        </fo:flow>
    </fo:page-sequence>
</fo:root>